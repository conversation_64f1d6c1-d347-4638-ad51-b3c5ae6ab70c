<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تشخيص مشاكل الأزرار</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            direction: rtl;
            background-color: #f5f5f5;
        }
        .debug-section {
            background: white;
            padding: 20px;
            margin: 10px 0;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        .status {
            padding: 10px;
            margin: 5px 0;
            border-radius: 3px;
        }
        .success { background-color: #d4edda; color: #155724; }
        .error { background-color: #f8d7da; color: #721c24; }
        .warning { background-color: #fff3cd; color: #856404; }
        button {
            padding: 10px 20px;
            margin: 5px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            color: white;
        }
        .test-btn { background-color: #007bff; }
        .add-btn { background-color: #4CAF50; }
        .table-view-btn { background-color: #673AB7; }
        .import-btn { background-color: #2196F3; }
        .remove-duplicates-btn { background-color: #9C27B0; }
        .search-btn { background-color: #f0f0f0; color: black; }
        #console-output {
            background: #000;
            color: #0f0;
            padding: 10px;
            font-family: monospace;
            height: 200px;
            overflow-y: auto;
            border-radius: 3px;
        }
    </style>
</head>
<body>
    <h1>تشخيص مشاكل الأزرار - نظام إدارة الموارد البشرية</h1>
    
    <div class="debug-section">
        <h2>1. اختبار وجود العناصر</h2>
        <button class="test-btn" onclick="checkElements()">فحص العناصر</button>
        <div id="elements-status"></div>
    </div>
    
    <div class="debug-section">
        <h2>2. اختبار الأزرار الأساسية</h2>
        <input type="text" id="searchInput" placeholder="بحث..." style="padding: 8px; margin: 10px;">
        <button class="search-btn" id="searchBtn">🔍</button>
        <button class="add-btn" id="addEmployeeBtn">+ موظف جديد</button>
        <button class="table-view-btn" id="tableViewBtn">عرض جدول</button>
        <button class="import-btn" id="importExcelBtn">استيراد من إكسل</button>
        <button class="remove-duplicates-btn" id="removeDuplicatesBtn">إزالة المكررين</button>
        <div id="buttons-status"></div>
    </div>
    
    <div class="debug-section">
        <h2>3. اختبار تحميل JavaScript</h2>
        <button class="test-btn" onclick="checkJavaScript()">فحص JavaScript</button>
        <div id="js-status"></div>
    </div>
    
    <div class="debug-section">
        <h2>4. سجل وحدة التحكم</h2>
        <button class="test-btn" onclick="clearConsole()">مسح السجل</button>
        <div id="console-output"></div>
    </div>
    
    <div class="debug-section">
        <h2>5. اختبار الصفحة الأصلية</h2>
        <button class="test-btn" onclick="window.open('/', '_blank')">فتح الصفحة الأصلية</button>
        <button class="test-btn" onclick="window.open('/test.html', '_blank')">فتح صفحة الاختبار</button>
    </div>

    <script>
        // إعادة توجيه console.log إلى الصفحة
        const originalLog = console.log;
        const originalError = console.error;
        const consoleOutput = document.getElementById('console-output');
        
        function addToConsole(message, type = 'log') {
            const div = document.createElement('div');
            div.style.color = type === 'error' ? '#ff6b6b' : '#0f0';
            div.textContent = new Date().toLocaleTimeString() + ' - ' + message;
            consoleOutput.appendChild(div);
            consoleOutput.scrollTop = consoleOutput.scrollHeight;
        }
        
        console.log = function(...args) {
            originalLog.apply(console, args);
            addToConsole(args.join(' '), 'log');
        };
        
        console.error = function(...args) {
            originalError.apply(console, args);
            addToConsole(args.join(' '), 'error');
        };

        function checkElements() {
            const elementsStatus = document.getElementById('elements-status');
            const requiredElements = [
                'searchBtn',
                'addEmployeeBtn', 
                'tableViewBtn',
                'importExcelBtn',
                'removeDuplicatesBtn',
                'searchInput'
            ];
            
            let html = '<h3>نتائج فحص العناصر:</h3>';
            let allFound = true;
            
            requiredElements.forEach(elementId => {
                const element = document.getElementById(elementId);
                if (element) {
                    html += `<div class="status success">✓ تم العثور على: ${elementId}</div>`;
                    console.log(`✓ تم العثور على العنصر: ${elementId}`);
                } else {
                    html += `<div class="status error">✗ مفقود: ${elementId}</div>`;
                    console.error(`✗ لم يتم العثور على العنصر: ${elementId}`);
                    allFound = false;
                }
            });
            
            if (allFound) {
                html += '<div class="status success">جميع العناصر موجودة!</div>';
            } else {
                html += '<div class="status error">بعض العناصر مفقودة!</div>';
            }
            
            elementsStatus.innerHTML = html;
        }

        function checkJavaScript() {
            const jsStatus = document.getElementById('js-status');
            let html = '<h3>نتائج فحص JavaScript:</h3>';
            
            // فحص المتغيرات العامة
            const globalVars = ['employees', 'activeEmployees', 'modalMode'];
            globalVars.forEach(varName => {
                if (typeof window[varName] !== 'undefined') {
                    html += `<div class="status success">✓ متغير موجود: ${varName}</div>`;
                } else {
                    html += `<div class="status warning">⚠ متغير مفقود: ${varName}</div>`;
                }
            });
            
            // فحص الدوال المطلوبة
            const requiredFunctions = [
                'openModal',
                'openTableModal', 
                'searchEmployees',
                'importFromExcel',
                'removeDuplicateEmployees'
            ];
            
            requiredFunctions.forEach(funcName => {
                if (typeof window[funcName] === 'function') {
                    html += `<div class="status success">✓ دالة موجودة: ${funcName}</div>`;
                } else {
                    html += `<div class="status error">✗ دالة مفقودة: ${funcName}</div>`;
                }
            });
            
            jsStatus.innerHTML = html;
        }

        function clearConsole() {
            document.getElementById('console-output').innerHTML = '';
        }

        // إعداد الأزرار للاختبار
        document.addEventListener('DOMContentLoaded', function() {
            console.log('تم تحميل صفحة التشخيص');
            
            // اختبار الأزرار
            const searchBtn = document.getElementById('searchBtn');
            if (searchBtn) {
                searchBtn.onclick = function() {
                    console.log('تم النقر على زر البحث - يعمل!');
                    document.getElementById('buttons-status').innerHTML = 
                        '<div class="status success">زر البحث يعمل بشكل صحيح!</div>';
                };
            }
            
            const addEmployeeBtn = document.getElementById('addEmployeeBtn');
            if (addEmployeeBtn) {
                addEmployeeBtn.onclick = function() {
                    console.log('تم النقر على زر إضافة موظف - يعمل!');
                    document.getElementById('buttons-status').innerHTML = 
                        '<div class="status success">زر إضافة موظف يعمل بشكل صحيح!</div>';
                };
            }
            
            const tableViewBtn = document.getElementById('tableViewBtn');
            if (tableViewBtn) {
                tableViewBtn.onclick = function() {
                    console.log('تم النقر على زر عرض الجدول - يعمل!');
                    document.getElementById('buttons-status').innerHTML = 
                        '<div class="status success">زر عرض الجدول يعمل بشكل صحيح!</div>';
                };
            }
            
            const importExcelBtn = document.getElementById('importExcelBtn');
            if (importExcelBtn) {
                importExcelBtn.onclick = function() {
                    console.log('تم النقر على زر استيراد من إكسل - يعمل!');
                    document.getElementById('buttons-status').innerHTML = 
                        '<div class="status success">زر استيراد من إكسل يعمل بشكل صحيح!</div>';
                };
            }
            
            const removeDuplicatesBtn = document.getElementById('removeDuplicatesBtn');
            if (removeDuplicatesBtn) {
                removeDuplicatesBtn.onclick = function() {
                    console.log('تم النقر على زر إزالة المكررين - يعمل!');
                    document.getElementById('buttons-status').innerHTML = 
                        '<div class="status success">زر إزالة المكررين يعمل بشكل صحيح!</div>';
                };
            }
            
            console.log('تم إعداد جميع أزرار الاختبار');
        });
    </script>
</body>
</html>
