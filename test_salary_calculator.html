<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار نافذة حساب الراتب</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            direction: rtl;
            min-height: 100vh;
        }

        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
            color: white;
            border-radius: 10px;
        }

        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            background: #f8f9fa;
        }

        .test-button {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            margin: 5px;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .test-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .test-button.secondary {
            background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
        }

        .test-button.warning {
            background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%);
            color: #212529;
        }

        .test-button.danger {
            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
        }

        .results-container {
            margin-top: 20px;
            padding: 20px;
            background: white;
            border-radius: 8px;
            border: 1px solid #dee2e6;
            max-height: 400px;
            overflow-y: auto;
        }

        .test-result {
            padding: 8px 12px;
            margin: 5px 0;
            border-radius: 4px;
            font-size: 14px;
        }

        .test-result.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .test-result.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .test-result.warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }

        .test-result.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }

        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            font-weight: bold;
        }

        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .status.warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }

        .demo-employee {
            background: #e3f2fd;
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
            border-left: 4px solid #2196F3;
        }

        .feature-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }

        .feature-item {
            background: white;
            padding: 15px;
            border-radius: 8px;
            border: 1px solid #dee2e6;
            text-align: center;
        }

        .feature-icon {
            font-size: 24px;
            margin-bottom: 10px;
            color: #007bff;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="fas fa-calculator"></i> اختبار نافذة حساب الراتب المحسنة</h1>
            <p>اختبار شامل للنافذة المبسطة والمحسنة لحساب الراتب</p>
        </div>

        <!-- معلومات الميزة -->
        <div class="test-section">
            <h2><i class="fas fa-info-circle"></i> نظرة عامة على الميزة المحسنة</h2>
            <p>نافذة حساب الراتب المبسطة والمحسنة تتضمن:</p>
            <div class="feature-list">
                <div class="feature-item">
                    <div class="feature-icon"><i class="fas fa-compress-alt"></i></div>
                    <h4>تصميم مبسط</h4>
                    <p>واجهة أكثر وضوحاً وأقل تعقيداً</p>
                </div>
                <div class="feature-item">
                    <div class="feature-icon"><i class="fas fa-table"></i></div>
                    <h4>جدول منظم</h4>
                    <p>عرض تفاصيل الراتب في جدول واضح</p>
                </div>
                <div class="feature-item">
                    <div class="feature-icon"><i class="fas fa-mobile-alt"></i></div>
                    <h4>تصميم متجاوب</h4>
                    <p>يعمل بشكل مثالي على جميع الأجهزة</p>
                </div>
                <div class="feature-item">
                    <div class="feature-icon"><i class="fas fa-eye"></i></div>
                    <h4>عرض ذكي</h4>
                    <p>إخفاء البنود الفارغة تلقائياً</p>
                </div>
                <div class="feature-item">
                    <div class="feature-icon"><i class="fas fa-calendar-check"></i></div>
                    <h4>عنوان ديناميكي</h4>
                    <p>يعرض الشهر والسنة الحالية</p>
                </div>
                <div class="feature-item">
                    <div class="feature-icon"><i class="fas fa-palette"></i></div>
                    <h4>ألوان محسنة</h4>
                    <p>ألوان أكثر هدوءاً وراحة للعين</p>
                </div>
            </div>

            <!-- التحسينات الجديدة -->
            <div style="margin-top: 20px; padding: 15px; background: #e8f5e8; border-radius: 8px; border-left: 4px solid #28a745;">
                <h4 style="margin: 0 0 10px 0; color: #155724;"><i class="fas fa-star"></i> التحسينات الجديدة:</h4>
                <ul style="margin: 0; padding-right: 20px; color: #155724;">
                    <li>تقليل حجم النافذة لتوفير مساحة أكبر</li>
                    <li>استخدام جدول بدلاً من البطاقات المنفصلة</li>
                    <li>إخفاء السلف والحوافز إذا كانت صفر</li>
                    <li>تحسين الألوان والخطوط</li>
                    <li>أزرار أصغر وأكثر أناقة</li>
                    <li>عنوان يعرض الشهر والسنة الحالية</li>
                </ul>
            </div>
        </div>

        <!-- بيانات تجريبية -->
        <div class="test-section">
            <h2><i class="fas fa-database"></i> البيانات التجريبية</h2>
            <div class="demo-employee">
                <h4>موظف تجريبي للاختبار:</h4>
                <p><strong>الاسم:</strong> أحمد محمد علي</p>
                <p><strong>الوظيفة:</strong> مهندس برمجيات</p>
                <p><strong>الكود:</strong> EMP001</p>
                <p><strong>الراتب الأساسي:</strong> 8,000 ج.م</p>
            </div>
            <button class="test-button" onclick="createTestData()">
                <i class="fas fa-plus"></i> إنشاء بيانات تجريبية
            </button>
        </div>

        <!-- اختبارات الوظائف -->
        <div class="test-section">
            <h2><i class="fas fa-vial"></i> اختبارات الوظائف</h2>

            <div style="margin-bottom: 15px;">
                <button class="test-button" onclick="testSalaryCalculatorFunction()">
                    <i class="fas fa-search"></i> اختبار وجود الدالة
                </button>
                <button class="test-button secondary" onclick="testOpenSalaryCalculator()">
                    <i class="fas fa-calculator"></i> اختبار فتح النافذة المحسنة
                </button>
                <button class="test-button warning" onclick="testSalaryCalculation()">
                    <i class="fas fa-chart-bar"></i> اختبار حساب الراتب
                </button>
                <button class="test-button danger" onclick="testErrorHandling()">
                    <i class="fas fa-exclamation-triangle"></i> اختبار معالجة الأخطاء
                </button>
            </div>

            <div style="margin-bottom: 15px;">
                <button class="test-button" onclick="testCurrentMonthYear()">
                    <i class="fas fa-calendar"></i> اختبار دالة الشهر والسنة
                </button>
                <button class="test-button secondary" onclick="testSimplifiedUI()">
                    <i class="fas fa-eye"></i> اختبار الواجهة المبسطة
                </button>
                <button class="test-button warning" onclick="testResponsiveDesign()">
                    <i class="fas fa-mobile-alt"></i> اختبار التصميم المتجاوب
                </button>
            </div>

            <div>
                <button class="test-button" onclick="runAllTests()">
                    <i class="fas fa-play"></i> تشغيل جميع الاختبارات
                </button>
                <button class="test-button secondary" onclick="clearResults()">
                    <i class="fas fa-trash"></i> مسح النتائج
                </button>
                <button class="test-button success" onclick="testEmployeeMonthly()">
                    <i class="fas fa-calendar-alt"></i> اختبار موظف شهري
                </button>
                <button class="test-button info" onclick="testEmployeeDaily()">
                    <i class="fas fa-clock"></i> اختبار موظف يومي
                </button>
                <button class="test-button warning" onclick="openOriginalApp()">
                    <i class="fas fa-external-link-alt"></i> فتح التطبيق الأصلي
                </button>
            </div>
        </div>

        <!-- نتائج الاختبارات -->
        <div class="results-container">
            <h3><i class="fas fa-clipboard-list"></i> سجل نتائج الاختبارات</h3>
            <div id="test-results">
                <div class="test-result info">🚀 جاهز لبدء اختبار نافذة حساب الراتب...</div>
            </div>
        </div>

        <!-- حالة الاختبارات -->
        <div style="margin-top: 20px;">
            <div id="function-status" class="status">⏳ في انتظار اختبار الدوال...</div>
            <div id="calculation-status" class="status">⏳ في انتظار اختبار الحسابات...</div>
            <div id="ui-status" class="status">⏳ في انتظار اختبار واجهة المستخدم...</div>
        </div>
    </div>

    <!-- تضمين ملف JavaScript الأصلي -->
    <script src="app.js"></script>

    <script>
        function addResult(message, type = 'info') {
            const results = document.getElementById('test-results');
            const div = document.createElement('div');
            div.className = `test-result ${type}`;
            div.textContent = new Date().toLocaleTimeString() + ' - ' + message;
            results.appendChild(div);
            results.scrollTop = results.scrollHeight;
        }

        function updateStatus(elementId, success, message) {
            const element = document.getElementById(elementId);
            if (success) {
                element.className = 'status success';
                element.textContent = '✅ ' + message;
            } else {
                element.className = 'status error';
                element.textContent = '❌ ' + message;
            }
        }

        function createTestData() {
            addResult('🔧 إنشاء بيانات تجريبية...', 'info');

            // إنشاء موظف تجريبي شهري
            const testEmployeeMonthly = {
                id: 999,
                name: 'أحمد محمد علي',
                position: 'مهندس برمجيات',
                employeeCode: 'EMP001',
                employmentType: 'monthly',
                basicSalary: 8000,
                nationalId: '12345678901234',
                phone: '01234567890'
            };

            // إنشاء موظف تجريبي يومي
            const testEmployeeDaily = {
                id: 998,
                name: 'سارة أحمد محمد',
                position: 'فنية صيانة',
                employeeCode: 'EMP002',
                employmentType: 'daily',
                nationalId: '12345678901235',
                phone: '01234567891',
                // بيانات الراتب من نافذة إدارة الراتب
                salary: {
                    dailyWage: 400, // الأجر اليومي من نافذة إدارة الراتب
                    workingDays: 26,
                    basic: 400 * 26, // الراتب الأساسي المحسوب
                    allowances: {
                        housing: 0,
                        transport: 0,
                        expat: 0,
                        meal: 0,
                        other: 0,
                        total: 0
                    },
                    total: 400 * 26 // إجمالي الراتب
                }
            };

            // إضافة الموظفين للقائمة إذا لم يكونوا موجودين
            if (!employees.find(emp => emp.id === 999)) {
                employees.push(testEmployeeMonthly);
                addResult('✅ تم إنشاء موظف تجريبي شهري', 'success');
            } else {
                addResult('ℹ️ الموظف التجريبي الشهري موجود بالفعل', 'info');
            }

            if (!employees.find(emp => emp.id === 998)) {
                employees.push(testEmployeeDaily);
                addResult('✅ تم إنشاء موظف تجريبي يومي', 'success');
            } else {
                addResult('ℹ️ الموظف التجريبي اليومي موجود بالفعل', 'info');
            }

            // إنشاء بيانات أيام تجريبية للموظف الشهري
            const testDaysDataMonthly = [
                {
                    id: Date.now() + 1,
                    employeeId: 999,
                    projectName: 'مشروع تطوير النظام',
                    startDate: '2024-01-01',
                    endDate: '2024-01-31',
                    calculatedDays: 31,
                    absenceDays: 2,
                    overtimeHours: 10,
                    createdAt: new Date().toISOString()
                },
                {
                    id: Date.now() + 2,
                    employeeId: 999,
                    projectName: 'مشروع الصيانة',
                    startDate: '2024-02-01',
                    endDate: '2024-02-28',
                    calculatedDays: 28,
                    absenceDays: 1,
                    overtimeHours: 8,
                    createdAt: new Date().toISOString()
                }
            ];

            // إنشاء بيانات أيام تجريبية للموظف اليومي
            const testDaysDataDaily = [
                {
                    id: Date.now() + 3,
                    employeeId: 998,
                    projectName: 'مشروع الصيانة اليومية',
                    startDate: '2024-01-01',
                    endDate: '2024-01-31',
                    calculatedDays: 20,
                    absenceDays: 1,
                    overtimeHours: 8,
                    createdAt: new Date().toISOString()
                }
            ];

            const testDaysData = [...testDaysDataMonthly, ...testDaysDataDaily];

            // إضافة بيانات الأيام
            if (typeof employeeDaysData === 'undefined') {
                window.employeeDaysData = [];
            }

            testDaysData.forEach(record => {
                if (!employeeDaysData.find(r => r.id === record.id)) {
                    employeeDaysData.push(record);
                }
            });

            addResult('✅ تم إنشاء بيانات أيام تجريبية', 'success');

            // إنشاء بيانات سلف وخصومات تجريبية
            const testAdvancesData = [
                {
                    id: Date.now() + 3,
                    employeeId: 999,
                    type: 'advance',
                    month: 1,
                    year: 2024,
                    amount: 1000,
                    description: 'سلفة شهر يناير',
                    date: new Date().toISOString()
                },
                {
                    id: Date.now() + 4,
                    employeeId: 999,
                    type: 'deduction',
                    month: 2,
                    year: 2024,
                    amount: 500,
                    description: 'خصم تأخير',
                    date: new Date().toISOString()
                }
            ];

            // إضافة بيانات السلف والخصومات
            if (typeof advancesDeductionsData === 'undefined') {
                window.advancesDeductionsData = [];
            }

            testAdvancesData.forEach(record => {
                if (!advancesDeductionsData.find(r => r.id === record.id)) {
                    advancesDeductionsData.push(record);
                }
            });

            addResult('✅ تم إنشاء بيانات سلف وخصومات تجريبية', 'success');

            // إنشاء بيانات حوافز ومكافآت تجريبية
            const testIncentivesData = [
                {
                    id: Date.now() + 5,
                    employeeId: 999,
                    type: 'incentive',
                    month: 1,
                    year: 2024,
                    amount: 800,
                    description: 'حافز الأداء',
                    date: new Date().toISOString()
                },
                {
                    id: Date.now() + 6,
                    employeeId: 999,
                    type: 'reward',
                    month: 2,
                    year: 2024,
                    amount: 600,
                    description: 'مكافأة التميز',
                    date: new Date().toISOString()
                }
            ];

            // إضافة بيانات الحوافز والمكافآت
            if (typeof incentivesRewardsData === 'undefined') {
                window.incentivesRewardsData = [];
            }

            testIncentivesData.forEach(record => {
                if (!incentivesRewardsData.find(r => r.id === record.id)) {
                    incentivesRewardsData.push(record);
                }
            });

            addResult('✅ تم إنشاء بيانات حوافز ومكافآت تجريبية', 'success');
            addResult('🎉 تم إنشاء جميع البيانات التجريبية بنجاح!', 'success');
        }

        function testSalaryCalculatorFunction() {
            addResult('🔍 بدء اختبار وجود دالة حساب الراتب...', 'info');

            let passed = 0;
            let total = 0;

            // اختبار وجود الدوال الأساسية
            const requiredFunctions = [
                'openSalaryCalculator',
                'createSalaryCalculatorModal',
                'closeSalaryCalculatorModal',
                'saveSalaryCalculation',
                'printSalaryCalculation',
                'exportSalaryCalculation'
            ];

            requiredFunctions.forEach(funcName => {
                total++;
                if (typeof window[funcName] === 'function') {
                    addResult(`✅ دالة موجودة: ${funcName}`, 'success');
                    passed++;
                } else {
                    addResult(`❌ دالة مفقودة: ${funcName}`, 'error');
                }
            });

            const percentage = (passed / total) * 100;
            addResult(`📊 النتيجة: ${passed}/${total} دالة موجودة (${percentage.toFixed(1)}%)`, 'info');

            const success = passed === total;
            updateStatus('function-status', success, success ? 'جميع الدوال موجودة' : `${passed}/${total} دالة موجودة`);

            return success;
        }

        function testOpenSalaryCalculator() {
            addResult('🔍 بدء اختبار فتح نافذة حساب الراتب...', 'info');

            try {
                // التحقق من وجود الدالة
                if (typeof openSalaryCalculator !== 'function') {
                    addResult('❌ دالة openSalaryCalculator غير موجودة', 'error');
                    updateStatus('ui-status', false, 'دالة فتح النافذة مفقودة');
                    return false;
                }

                // اختبار فتح النافذة للموظف التجريبي
                addResult('🔧 محاولة فتح النافذة للموظف التجريبي...', 'info');
                openSalaryCalculator(999);

                // التحقق من وجود النافذة
                setTimeout(() => {
                    const modal = document.getElementById('salaryCalculatorModal');
                    if (modal) {
                        addResult('✅ تم فتح نافذة حساب الراتب بنجاح', 'success');
                        addResult('✅ النافذة تحتوي على جميع العناصر المطلوبة', 'success');
                        updateStatus('ui-status', true, 'واجهة المستخدم تعمل بشكل صحيح');

                        // إغلاق النافذة بعد 3 ثوان
                        setTimeout(() => {
                            closeSalaryCalculatorModal();
                            addResult('✅ تم إغلاق النافذة تلقائياً', 'info');
                        }, 3000);
                    } else {
                        addResult('❌ فشل في فتح النافذة', 'error');
                        updateStatus('ui-status', false, 'فشل في فتح النافذة');
                    }
                }, 500);

                return true;
            } catch (error) {
                addResult(`❌ خطأ في فتح النافذة: ${error.message}`, 'error');
                updateStatus('ui-status', false, 'خطأ في فتح النافذة');
                return false;
            }
        }

        function testSalaryCalculation() {
            addResult('🔍 بدء اختبار حساب الراتب...', 'info');

            try {
                // التحقق من وجود البيانات التجريبية
                if (!employees.find(emp => emp.id === 999)) {
                    addResult('⚠️ لا توجد بيانات تجريبية، سيتم إنشاؤها...', 'warning');
                    createTestData();
                }

                // محاكاة حساب الراتب
                const employee = employees.find(emp => emp.id === 999);
                if (!employee) {
                    addResult('❌ لم يتم العثور على الموظف التجريبي', 'error');
                    return false;
                }

                // حساب البيانات المتوقعة
                const basicSalary = employee.basicSalary || 8000;
                const employmentType = employee.employmentType || 'monthly';

                // حساب الأيام من البيانات التجريبية
                const employeeRecords = employeeDaysData.filter(record =>
                    parseInt(record.employeeId) === 999
                );

                const totalDays = employeeRecords.reduce((sum, record) => sum + (record.calculatedDays || 0), 0);
                const totalAbsence = employeeRecords.reduce((sum, record) => sum + (record.absenceDays || 0), 0);
                const totalActual = totalDays - totalAbsence;
                const totalOvertime = employeeRecords.reduce((sum, record) => sum + (record.overtimeHours || 0), 0);

                addResult(`📊 نوع التوظيف: ${employmentType === 'daily' ? 'يومي' : 'شهري'}`, 'info');
                addResult(`📊 ${employmentType === 'daily' ? 'أجر اليوم' : 'الراتب الأساسي الشهري'}: ${basicSalary} ج.م`, 'info');
                addResult(`📊 إجمالي الأيام: ${totalDays}`, 'info');
                addResult(`📊 أيام الغياب: ${totalAbsence}`, 'info');
                addResult(`📊 الأيام الفعلية: ${totalActual}`, 'info');
                addResult(`📊 الساعات الإضافية: ${totalOvertime}`, 'info');

                // حساب الراتب حسب نوع التوظيف
                let salaryFromDays = 0;
                if (employmentType === 'daily') {
                    // للموظف اليومي: عدد الأيام المحسوبة × الأجر اليومي
                    salaryFromDays = totalDays * basicSalary;
                    addResult(`💰 راتب الأيام (${totalDays} يوم × ${basicSalary} ج.م): ${salaryFromDays.toFixed(2)} ج.م`, 'success');
                } else {
                    // للموظف الشهري: الراتب الشهري - (أيام الغياب × قيمة اليوم الواحد)
                    const dailyValue = basicSalary / 30;
                    const absenceDeduction = totalAbsence * dailyValue;
                    salaryFromDays = basicSalary - absenceDeduction;
                    addResult(`💰 الراتب الشهري (${basicSalary} ج.م) - خصم الغياب (${totalAbsence} يوم × ${dailyValue.toFixed(2)} ج.م): ${salaryFromDays.toFixed(2)} ج.م`, 'success');
                }

                const overtimeRate = (employmentType === 'daily' ? basicSalary : basicSalary / 30) / 8;
                const overtimePay = totalOvertime * overtimeRate;
                addResult(`💰 راتب الساعات الإضافية: ${overtimePay.toFixed(2)} ج.م`, 'success');

                // حساب السلف والخصومات
                const employeeAdvances = advancesDeductionsData.filter(record =>
                    parseInt(record.employeeId) === 999
                );

                const totalAdvances = employeeAdvances
                    .filter(record => record.type === 'advance')
                    .reduce((sum, record) => sum + (record.amount || 0), 0);
                const totalDeductions = employeeAdvances
                    .filter(record => record.type === 'deduction')
                    .reduce((sum, record) => sum + (record.amount || 0), 0);

                addResult(`💸 إجمالي السلف: ${totalAdvances} ج.م`, 'warning');
                addResult(`💸 إجمالي الخصومات: ${totalDeductions} ج.م`, 'warning');

                // حساب الحوافز والمكافآت
                const employeeIncentives = incentivesRewardsData.filter(record =>
                    parseInt(record.employeeId) === 999
                );

                const totalIncentives = employeeIncentives
                    .filter(record => record.type === 'incentive')
                    .reduce((sum, record) => sum + (record.amount || 0), 0);
                const totalRewards = employeeIncentives
                    .filter(record => record.type === 'reward')
                    .reduce((sum, record) => sum + (record.amount || 0), 0);

                addResult(`🎁 إجمالي الحوافز: ${totalIncentives} ج.م`, 'success');
                addResult(`🎁 إجمالي المكافآت: ${totalRewards} ج.م`, 'success');

                // حساب الراتب الإجمالي
                const totalSalary = salaryFromDays + overtimePay + totalIncentives + totalRewards - totalAdvances - totalDeductions;

                addResult(`🏆 إجمالي الراتب المستحق: ${totalSalary.toFixed(2)} ج.م`, 'success');

                updateStatus('calculation-status', true, 'حسابات الراتب تعمل بشكل صحيح');
                return true;

            } catch (error) {
                addResult(`❌ خطأ في حساب الراتب: ${error.message}`, 'error');
                updateStatus('calculation-status', false, 'خطأ في حسابات الراتب');
                return false;
            }
        }

        function testErrorHandling() {
            addResult('🔍 بدء اختبار معالجة الأخطاء...', 'info');

            let passed = 0;
            let total = 0;

            // اختبار 1: معرف موظف غير صحيح
            total++;
            try {
                addResult('🧪 اختبار معرف موظف غير صحيح...', 'info');
                openSalaryCalculator(null);
                addResult('✅ تم التعامل مع معرف الموظف الفارغ', 'success');
                passed++;
            } catch (error) {
                addResult('❌ خطأ في التعامل مع معرف الموظف الفارغ', 'error');
            }

            // اختبار 2: معرف موظف غير موجود
            total++;
            try {
                addResult('🧪 اختبار معرف موظف غير موجود...', 'info');
                openSalaryCalculator(99999);
                addResult('✅ تم التعامل مع معرف الموظف غير الموجود', 'success');
                passed++;
            } catch (error) {
                addResult('❌ خطأ في التعامل مع معرف الموظف غير الموجود', 'error');
            }

            // اختبار 3: بيانات مفقودة
            total++;
            try {
                addResult('🧪 اختبار التعامل مع البيانات المفقودة...', 'info');
                // محاكاة بيانات مفقودة
                const originalData = window.employeeDaysData;
                window.employeeDaysData = [];

                openSalaryCalculator(999);

                // استعادة البيانات
                window.employeeDaysData = originalData;

                addResult('✅ تم التعامل مع البيانات المفقودة', 'success');
                passed++;
            } catch (error) {
                addResult('❌ خطأ في التعامل مع البيانات المفقودة', 'error');
            }

            const percentage = (passed / total) * 100;
            addResult(`📊 نتيجة اختبار معالجة الأخطاء: ${passed}/${total} (${percentage.toFixed(1)}%)`, 'info');

            return passed === total;
        }

        function runAllTests() {
            addResult('🚀 بدء تشغيل جميع الاختبارات المحسنة...', 'info');

            // إنشاء البيانات التجريبية أولاً
            createTestData();

            setTimeout(() => {
                const test1 = testSalaryCalculatorFunction();
                setTimeout(() => {
                    const test2 = testCurrentMonthYear();
                    setTimeout(() => {
                        const test3 = testSalaryCalculation();
                        setTimeout(() => {
                            const test4 = testErrorHandling();
                            setTimeout(() => {
                                testSimplifiedUI();
                                setTimeout(() => {
                                    testResponsiveDesign();
                                    setTimeout(() => {
                                        testOpenSalaryCalculator();

                                        setTimeout(() => {
                                            addResult('🎉 انتهاء جميع الاختبارات المحسنة!', 'success');

                                            const allPassed = test1 && test2 && test3 && test4;
                                            if (allPassed) {
                                                addResult('✅ جميع الاختبارات نجحت! نافذة حساب الراتب المحسنة جاهزة للاستخدام', 'success');
                                            } else {
                                                addResult('⚠️ بعض الاختبارات فشلت، يرجى مراجعة النتائج', 'warning');
                                            }
                                        }, 1000);
                                    }, 1500);
                                }, 1500);
                            }, 1000);
                        }, 1000);
                    }, 1000);
                }, 1000);
            }, 500);
        }

        function clearResults() {
            document.getElementById('test-results').innerHTML =
                '<div class="test-result info">🚀 جاهز لبدء اختبار نافذة حساب الراتب...</div>';

            // إعادة تعيين حالة الاختبارات
            document.getElementById('function-status').className = 'status';
            document.getElementById('function-status').textContent = '⏳ في انتظار اختبار الدوال...';

            document.getElementById('calculation-status').className = 'status';
            document.getElementById('calculation-status').textContent = '⏳ في انتظار اختبار الحسابات...';

            document.getElementById('ui-status').className = 'status';
            document.getElementById('ui-status').textContent = '⏳ في انتظار اختبار واجهة المستخدم...';
        }

        function openOriginalApp() {
            window.open('/', '_blank');
        }

        // اختبار الموظف الشهري
        function testEmployeeMonthly() {
            addResult('🔍 بدء اختبار حساب راتب الموظف الشهري...', 'info');

            // إنشاء البيانات التجريبية أولاً
            createTestData();

            // فتح نافذة حساب الراتب للموظف الشهري
            setTimeout(() => {
                if (typeof openSalaryCalculator === 'function') {
                    openSalaryCalculator(999);
                    addResult('✅ تم فتح نافذة حساب الراتب للموظف الشهري', 'success');
                } else {
                    addResult('❌ دالة openSalaryCalculator غير موجودة', 'error');
                }
            }, 500);
        }

        // اختبار الموظف اليومي
        function testEmployeeDaily() {
            addResult('🔍 بدء اختبار حساب راتب الموظف اليومي...', 'info');

            // إنشاء البيانات التجريبية أولاً
            createTestData();

            // التحقق من بيانات الموظف اليومي
            setTimeout(() => {
                const dailyEmployee = employees.find(emp => emp.id === 998);
                if (dailyEmployee) {
                    addResult(`📊 بيانات الموظف اليومي: ${dailyEmployee.name}`, 'info');
                    addResult(`📊 نوع التوظيف: ${dailyEmployee.employmentType}`, 'info');

                    // عرض الأجر اليومي من نافذة إدارة الراتب
                    if (dailyEmployee.salary && dailyEmployee.salary.dailyWage) {
                        addResult(`📊 أجر اليوم (من نافذة إدارة الراتب): ${dailyEmployee.salary.dailyWage} ج.م`, 'info');
                        addResult(`📊 إجمالي الراتب الشهري: ${dailyEmployee.salary.total} ج.م`, 'info');
                    } else {
                        addResult(`📊 أجر اليوم (افتراضي): 100 ج.م`, 'warning');
                        addResult(`⚠️ لم يتم تحديد راتب في نافذة إدارة الراتب`, 'warning');
                    }
                } else {
                    addResult('❌ لم يتم العثور على الموظف اليومي', 'error');
                }

                // فتح نافذة حساب الراتب للموظف اليومي
                if (typeof openSalaryCalculator === 'function') {
                    openSalaryCalculator(998);
                    addResult('✅ تم فتح نافذة حساب الراتب للموظف اليومي', 'success');
                } else {
                    addResult('❌ دالة openSalaryCalculator غير موجودة', 'error');
                }
            }, 500);
        }

        // اختبار دالة الشهر والسنة الحالية
        function testCurrentMonthYear() {
            addResult('🔍 بدء اختبار دالة الشهر والسنة...', 'info');

            try {
                if (typeof getCurrentMonthYear !== 'function') {
                    addResult('❌ دالة getCurrentMonthYear غير موجودة', 'error');
                    return false;
                }

                const monthYear = getCurrentMonthYear();
                addResult(`✅ الشهر والسنة الحالية: ${monthYear}`, 'success');

                // التحقق من صيغة النتيجة
                const pattern = /^(يناير|فبراير|مارس|أبريل|مايو|يونيو|يوليو|أغسطس|سبتمبر|أكتوبر|نوفمبر|ديسمبر) \d{4}$/;
                if (pattern.test(monthYear)) {
                    addResult('✅ صيغة الشهر والسنة صحيحة', 'success');
                    return true;
                } else {
                    addResult('❌ صيغة الشهر والسنة غير صحيحة', 'error');
                    return false;
                }
            } catch (error) {
                addResult(`❌ خطأ في اختبار دالة الشهر والسنة: ${error.message}`, 'error');
                return false;
            }
        }

        // اختبار الواجهة المبسطة
        function testSimplifiedUI() {
            addResult('🔍 بدء اختبار الواجهة المبسطة...', 'info');

            try {
                // إنشاء بيانات تجريبية إذا لم تكن موجودة
                if (!employees.find(emp => emp.id === 999)) {
                    createTestData();
                }

                // فتح النافذة
                openSalaryCalculator(999);

                setTimeout(() => {
                    const modal = document.getElementById('salaryCalculatorModal');
                    if (!modal) {
                        addResult('❌ لم يتم فتح النافذة', 'error');
                        return false;
                    }

                    // اختبار عناصر الواجهة المبسطة
                    const tests = [
                        {
                            selector: 'table',
                            name: 'جدول تفاصيل الراتب',
                            expected: true
                        },
                        {
                            selector: '.modal-header h2',
                            name: 'عنوان النافذة',
                            expected: true,
                            textCheck: (el) => el.textContent.includes('حساب راتب شهر')
                        },
                        {
                            selector: 'button',
                            name: 'أزرار الإجراءات',
                            expected: true,
                            count: 3
                        }
                    ];

                    let passed = 0;
                    tests.forEach(test => {
                        const elements = modal.querySelectorAll(test.selector);
                        if (test.expected && elements.length > 0) {
                            if (test.count && elements.length >= test.count) {
                                addResult(`✅ ${test.name}: موجود (${elements.length} عنصر)`, 'success');
                                passed++;
                            } else if (!test.count) {
                                if (test.textCheck && !test.textCheck(elements[0])) {
                                    addResult(`❌ ${test.name}: النص غير صحيح`, 'error');
                                } else {
                                    addResult(`✅ ${test.name}: موجود`, 'success');
                                    passed++;
                                }
                            } else {
                                addResult(`❌ ${test.name}: عدد غير كافي (${elements.length}/${test.count})`, 'error');
                            }
                        } else {
                            addResult(`❌ ${test.name}: غير موجود`, 'error');
                        }
                    });

                    // إغلاق النافذة
                    closeSalaryCalculatorModal();

                    const success = passed === tests.length;
                    addResult(`📊 نتيجة اختبار الواجهة المبسطة: ${passed}/${tests.length}`, success ? 'success' : 'warning');
                    return success;

                }, 1000);

            } catch (error) {
                addResult(`❌ خطأ في اختبار الواجهة المبسطة: ${error.message}`, 'error');
                return false;
            }
        }

        // اختبار التصميم المتجاوب
        function testResponsiveDesign() {
            addResult('🔍 بدء اختبار التصميم المتجاوب...', 'info');

            try {
                // إنشاء بيانات تجريبية إذا لم تكن موجودة
                if (!employees.find(emp => emp.id === 999)) {
                    createTestData();
                }

                // فتح النافذة
                openSalaryCalculator(999);

                setTimeout(() => {
                    const modal = document.getElementById('salaryCalculatorModal');
                    if (!modal) {
                        addResult('❌ لم يتم فتح النافذة', 'error');
                        return false;
                    }

                    const modalContent = modal.querySelector('.modal-content');
                    if (!modalContent) {
                        addResult('❌ محتوى النافذة غير موجود', 'error');
                        return false;
                    }

                    // اختبار الأبعاد
                    const style = window.getComputedStyle(modalContent);
                    const width = style.width;
                    const maxWidth = style.maxWidth;

                    addResult(`📏 عرض النافذة: ${width}`, 'info');
                    addResult(`📏 الحد الأقصى للعرض: ${maxWidth}`, 'info');

                    // التحقق من التصميم المتجاوب
                    let passed = 0;
                    let total = 0;

                    // اختبار 1: الحد الأقصى للعرض
                    total++;
                    if (maxWidth === '800px') {
                        addResult('✅ الحد الأقصى للعرض صحيح (800px)', 'success');
                        passed++;
                    } else {
                        addResult(`❌ الحد الأقصى للعرض غير صحيح: ${maxWidth}`, 'error');
                    }

                    // اختبار 2: العرض النسبي
                    total++;
                    const widthPercent = parseFloat(width) / window.innerWidth * 100;
                    if (widthPercent <= 85) {
                        addResult(`✅ العرض النسبي صحيح (${widthPercent.toFixed(1)}%)`, 'success');
                        passed++;
                    } else {
                        addResult(`❌ العرض النسبي كبير جداً (${widthPercent.toFixed(1)}%)`, 'error');
                    }

                    // اختبار 3: الشبكة المتجاوبة
                    total++;
                    const gridElements = modal.querySelectorAll('[style*="grid-template-columns"]');
                    if (gridElements.length > 0) {
                        addResult('✅ الشبكة المتجاوبة موجودة', 'success');
                        passed++;
                    } else {
                        addResult('❌ الشبكة المتجاوبة غير موجودة', 'error');
                    }

                    // إغلاق النافذة
                    closeSalaryCalculatorModal();

                    const success = passed === total;
                    addResult(`📊 نتيجة اختبار التصميم المتجاوب: ${passed}/${total}`, success ? 'success' : 'warning');
                    return success;

                }, 1000);

            } catch (error) {
                addResult(`❌ خطأ في اختبار التصميم المتجاوب: ${error.message}`, 'error');
                return false;
            }
        }

        // تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            addResult('تم تحميل صفحة اختبار نافذة حساب الراتب المحسنة', 'info');
            addResult('جاهز لبدء الاختبارات المحسنة...', 'info');

            // اختبار سريع تلقائي
            setTimeout(() => {
                addResult('--- اختبار تلقائي سريع للنافذة المحسنة ---', 'info');
                if (typeof openSalaryCalculator === 'function') {
                    addResult('✅ دالة حساب الراتب المحسنة متاحة', 'success');
                } else {
                    addResult('❌ دالة حساب الراتب غير متاحة', 'error');
                }

                if (typeof getCurrentMonthYear === 'function') {
                    addResult('✅ دالة الشهر والسنة متاحة', 'success');
                } else {
                    addResult('❌ دالة الشهر والسنة غير متاحة', 'error');
                }
            }, 1000);
        });
    </script>
</body>
</html>
