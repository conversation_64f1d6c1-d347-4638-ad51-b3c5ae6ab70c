<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار حساب الراتب الشامل مع جميع البدلات</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background: #f8f9fa;
            direction: rtl;
        }
        .container {
            max-width: 900px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            background: #f8f9fa;
        }
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        .test-button:hover {
            background: #0056b3;
        }
        .success { color: #28a745; }
        .error { color: #dc3545; }
        .warning { color: #ffc107; }
        .info { color: #17a2b8; }
        .result {
            margin: 5px 0;
            padding: 8px;
            border-radius: 4px;
            font-size: 14px;
        }
        .result.success { background: #d4edda; border: 1px solid #c3e6cb; }
        .result.error { background: #f8d7da; border: 1px solid #f5c6cb; }
        .result.warning { background: #fff3cd; border: 1px solid #ffeaa7; }
        .result.info { background: #d1ecf1; border: 1px solid #bee5eb; }
        .summary {
            background: linear-gradient(135deg, #007bff, #0056b3);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            text-align: center;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧮 اختبار حساب الراتب الشامل مع جميع البدلات</h1>
        <p>هذه الصفحة لاختبار أن جميع البدلات والحوافز والخصومات تظهر بشكل صحيح في نافذة حساب الراتب</p>

        <div class="summary">
            <h2>📋 البدلات المتضمنة في النظام</h2>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin-top: 15px;">
                <div>✅ الراتب الأساسي</div>
                <div>🌍 بدل الاغتراب اليومي</div>
                <div>🚗 بدل الانتقالات اليومي</div>
                <div>🏠 بدل السكن الثابت</div>
                <div>🚌 بدل المواصلات الثابت</div>
                <div>🍽️ بدل الوجبات</div>
                <div>💼 بدلات أخرى</div>
                <div>⏰ الساعات الإضافية</div>
                <div>🎯 الحوافز</div>
                <div>🏅 المكافآت</div>
                <div>💰 السلف</div>
                <div>📉 الخصومات</div>
            </div>
        </div>

        <div class="test-section">
            <h3>🔧 إعداد البيانات التجريبية</h3>
            <button class="test-button" onclick="createCompleteTestEmployee()">إنشاء موظف شامل</button>
            <button class="test-button" onclick="addTestWorkDays()">إضافة أيام العمل</button>
            <button class="test-button" onclick="addTestIncentives()">إضافة حوافز ومكافآت</button>
            <button class="test-button" onclick="addTestAdvancesDeductions()">إضافة سلف وخصومات</button>
        </div>

        <div class="test-section">
            <h3>🧮 اختبار حساب الراتب</h3>
            <button class="test-button" onclick="testCompleteSalaryCalculation()">اختبار حساب الراتب الشامل</button>
            <button class="test-button" onclick="showSalaryBreakdown()">عرض تفصيل الراتب</button>
            <button class="test-button" onclick="clearResults()">مسح النتائج</button>
        </div>

        <div id="results"></div>
    </div>

    <!-- تحميل ملف التطبيق الرئيسي -->
    <script src="app.js"></script>

    <script>
        const TEST_EMPLOYEE_ID = 88888;

        // دالة لإضافة نتيجة
        function addResult(message, type = 'info') {
            const resultsDiv = document.getElementById('results');
            const resultDiv = document.createElement('div');
            resultDiv.className = `result ${type}`;
            resultDiv.innerHTML = message;
            resultsDiv.appendChild(resultDiv);
            console.log(`[${type.toUpperCase()}] ${message}`);
        }

        // إنشاء موظف تجريبي شامل
        function createCompleteTestEmployee() {
            addResult('👤 إنشاء موظف تجريبي شامل...', 'info');

            try {
                // التأكد من وجود مصفوفة الموظفين
                if (typeof employees === 'undefined') {
                    window.employees = [];
                }

                // حذف الموظف التجريبي إذا كان موجوداً
                const existingIndex = employees.findIndex(emp => emp.id === TEST_EMPLOYEE_ID);
                if (existingIndex !== -1) {
                    employees.splice(existingIndex, 1);
                    addResult('🗑️ تم حذف الموظف التجريبي السابق', 'info');
                }

                // إنشاء موظف تجريبي شامل
                const testEmployee = {
                    id: TEST_EMPLOYEE_ID,
                    name: 'أحمد محمد الشامل',
                    employeeCode: 'COMPLETE-88888',
                    position: 'مدير مشاريع',
                    employmentType: 'monthly',
                    basicSalary: 12000,
                    salary: {
                        basic: 12000,
                        total: 12000,
                        // البدلات الثابتة
                        allowances: {
                            housing: 800,        // بدل السكن
                            transport: 400,      // بدل المواصلات الثابت
                            meal: 300,          // بدل الوجبات
                            other: 200,         // بدلات أخرى
                            total: 1700         // إجمالي البدلات الثابتة
                        },
                        // البدلات اليومية
                        dailyAllowances: {
                            expatDaily: 100,     // بدل اغتراب يومي
                            transportDaily: 60   // بدل انتقالات يومي
                        }
                    },
                    active: true,
                    createdAt: new Date().toISOString()
                };

                employees.push(testEmployee);

                // حفظ البيانات
                if (typeof saveEmployeesToLocalStorage === 'function') {
                    saveEmployeesToLocalStorage();
                }

                addResult('✅ تم إنشاء الموظف التجريبي الشامل بنجاح', 'success');
                addResult(`💰 الراتب الأساسي: ${testEmployee.salary.basic.toLocaleString()} ج.م`, 'info');
                addResult(`🏠 البدلات الثابتة: ${testEmployee.salary.allowances.total.toLocaleString()} ج.م`, 'info');
                addResult(`🌍 بدل الاغتراب اليومي: ${testEmployee.salary.dailyAllowances.expatDaily} ج.م`, 'success');
                addResult(`🚗 بدل الانتقالات اليومي: ${testEmployee.salary.dailyAllowances.transportDaily} ج.م`, 'success');

            } catch (error) {
                addResult(`❌ خطأ في إنشاء الموظف التجريبي: ${error.message}`, 'error');
            }
        }

        // إضافة أيام العمل التجريبية
        function addTestWorkDays() {
            addResult('📅 إضافة أيام العمل التجريبية...', 'info');

            try {
                if (typeof employeeDaysData === 'undefined') {
                    window.employeeDaysData = [];
                }

                // حذف البيانات السابقة للموظف التجريبي
                window.employeeDaysData = employeeDaysData.filter(record => 
                    parseInt(record.employeeId) !== TEST_EMPLOYEE_ID
                );

                // إضافة بيانات أيام عمل جديدة
                const workDaysRecord = {
                    id: Date.now(),
                    employeeId: TEST_EMPLOYEE_ID,
                    month: new Date().getMonth() + 1,
                    year: new Date().getFullYear(),
                    calculatedDays: 25,
                    absenceDays: 3,
                    actualDays: 22,
                    overtimeHours: 20,
                    project: 'مشروع شامل تجريبي',
                    createdAt: new Date().toISOString()
                };

                employeeDaysData.push(workDaysRecord);

                // حفظ البيانات
                if (typeof saveDaysDataToLocalStorage === 'function') {
                    saveDaysDataToLocalStorage();
                }

                addResult('✅ تم إضافة أيام العمل التجريبية', 'success');
                addResult(`📊 أيام العمل الفعلية: ${workDaysRecord.actualDays} يوم`, 'info');
                addResult(`⏰ الساعات الإضافية: ${workDaysRecord.overtimeHours} ساعة`, 'info');

            } catch (error) {
                addResult(`❌ خطأ في إضافة أيام العمل: ${error.message}`, 'error');
            }
        }

        // إضافة حوافز ومكافآت تجريبية
        function addTestIncentives() {
            addResult('🏆 إضافة حوافز ومكافآت تجريبية...', 'info');

            try {
                if (typeof incentivesRewardsData === 'undefined') {
                    window.incentivesRewardsData = [];
                }

                const currentMonth = new Date().getMonth() + 1;
                const currentYear = new Date().getFullYear();

                // حذف البيانات السابقة للموظف التجريبي
                window.incentivesRewardsData = incentivesRewardsData.filter(record => 
                    parseInt(record.employeeId) !== TEST_EMPLOYEE_ID
                );

                // إضافة حوافز
                const incentiveRecord = {
                    id: Date.now(),
                    employeeId: TEST_EMPLOYEE_ID,
                    type: 'incentive',
                    month: currentMonth,
                    year: currentYear,
                    amount: 1500,
                    description: 'حافز الأداء المتميز',
                    createdAt: new Date().toISOString()
                };

                // إضافة مكافآت
                const rewardRecord = {
                    id: Date.now() + 1,
                    employeeId: TEST_EMPLOYEE_ID,
                    type: 'reward',
                    month: currentMonth,
                    year: currentYear,
                    amount: 1000,
                    description: 'مكافأة إنجاز المشروع',
                    createdAt: new Date().toISOString()
                };

                incentivesRewardsData.push(incentiveRecord, rewardRecord);

                // حفظ البيانات
                if (typeof saveIncentivesRewardsToLocalStorage === 'function') {
                    saveIncentivesRewardsToLocalStorage();
                }

                addResult('✅ تم إضافة الحوافز والمكافآت التجريبية', 'success');
                addResult(`🎯 الحوافز: ${incentiveRecord.amount.toLocaleString()} ج.م`, 'info');
                addResult(`🏅 المكافآت: ${rewardRecord.amount.toLocaleString()} ج.م`, 'info');

            } catch (error) {
                addResult(`❌ خطأ في إضافة الحوافز والمكافآت: ${error.message}`, 'error');
            }
        }

        // إضافة سلف وخصومات تجريبية
        function addTestAdvancesDeductions() {
            addResult('💸 إضافة سلف وخصومات تجريبية...', 'info');

            try {
                if (typeof advancesDeductionsData === 'undefined') {
                    window.advancesDeductionsData = [];
                }

                const currentMonth = new Date().getMonth() + 1;
                const currentYear = new Date().getFullYear();

                // حذف البيانات السابقة للموظف التجريبي
                window.advancesDeductionsData = advancesDeductionsData.filter(record => 
                    parseInt(record.employeeId) !== TEST_EMPLOYEE_ID
                );

                // إضافة سلفة
                const advanceRecord = {
                    id: Date.now(),
                    employeeId: TEST_EMPLOYEE_ID,
                    type: 'advance',
                    month: currentMonth,
                    year: currentYear,
                    amount: 2000,
                    description: 'سلفة شخصية',
                    createdAt: new Date().toISOString()
                };

                // إضافة خصم
                const deductionRecord = {
                    id: Date.now() + 1,
                    employeeId: TEST_EMPLOYEE_ID,
                    type: 'deduction',
                    month: currentMonth,
                    year: currentYear,
                    amount: 500,
                    description: 'خصم تأخير',
                    createdAt: new Date().toISOString()
                };

                advancesDeductionsData.push(advanceRecord, deductionRecord);

                // حفظ البيانات
                if (typeof saveAdvancesDeductionsToLocalStorage === 'function') {
                    saveAdvancesDeductionsToLocalStorage();
                }

                addResult('✅ تم إضافة السلف والخصومات التجريبية', 'success');
                addResult(`💰 السلف: ${advanceRecord.amount.toLocaleString()} ج.م`, 'warning');
                addResult(`📉 الخصومات: ${deductionRecord.amount.toLocaleString()} ج.م`, 'warning');

            } catch (error) {
                addResult(`❌ خطأ في إضافة السلف والخصومات: ${error.message}`, 'error');
            }
        }

        // اختبار حساب الراتب الشامل
        function testCompleteSalaryCalculation() {
            addResult('🧮 اختبار حساب الراتب الشامل...', 'info');

            try {
                // التحقق من وجود الموظف التجريبي
                const testEmployee = employees.find(emp => emp.id === TEST_EMPLOYEE_ID);
                if (!testEmployee) {
                    addResult('❌ الموظف التجريبي غير موجود، يرجى إنشاؤه أولاً', 'error');
                    return;
                }

                // فتح نافذة حساب الراتب
                addResult('🖥️ فتح نافذة حساب الراتب الشامل...', 'info');
                openSalaryCalculator(TEST_EMPLOYEE_ID);

                // التحقق من النافذة بعد ثانية
                setTimeout(() => {
                    const modal = document.getElementById('salaryCalculatorModal');
                    if (modal) {
                        addResult('✅ تم فتح نافذة حساب الراتب بنجاح', 'success');
                        
                        // فحص محتوى النافذة
                        const modalContent = modal.innerHTML;
                        
                        // قائمة البدلات المطلوب فحصها
                        const allowancesToCheck = [
                            { name: 'الراتب الأساسي', text: 'الراتب الأساسي' },
                            { name: 'البدلات اليومية', text: 'البدلات اليومية' },
                            { name: 'بدل الاغتراب', text: 'بدل الاغتراب' },
                            { name: 'بدل الانتقالات', text: 'بدل الانتقالات' },
                            { name: 'البدلات الثابتة', text: 'البدلات الثابتة' },
                            { name: 'بدل السكن', text: 'بدل السكن' },
                            { name: 'بدل المواصلات الثابت', text: 'بدل المواصلات الثابت' },
                            { name: 'بدل الوجبات', text: 'بدل الوجبات' },
                            { name: 'الساعات الإضافية', text: 'الساعات الإضافية' },
                            { name: 'الحوافز والمكافآت', text: 'الحوافز والمكافآت' },
                            { name: 'السلف والخصومات', text: 'السلف والخصومات' }
                        ];

                        let foundCount = 0;
                        allowancesToCheck.forEach(allowance => {
                            if (modalContent.includes(allowance.text)) {
                                addResult(`✅ ${allowance.name} موجود في النافذة`, 'success');
                                foundCount++;
                            } else {
                                addResult(`❌ ${allowance.name} غير موجود في النافذة`, 'error');
                            }
                        });

                        addResult(`📊 نتيجة الفحص: ${foundCount}/${allowancesToCheck.length} بدل موجود`, 
                            foundCount === allowancesToCheck.length ? 'success' : 'warning');

                        if (foundCount === allowancesToCheck.length) {
                            addResult('🎉 جميع البدلات تظهر بشكل صحيح!', 'success');
                        }
                        
                    } else {
                        addResult('❌ فشل في فتح نافذة حساب الراتب', 'error');
                    }
                }, 1000);

            } catch (error) {
                addResult(`❌ خطأ في اختبار حساب الراتب: ${error.message}`, 'error');
            }
        }

        // عرض تفصيل الراتب
        function showSalaryBreakdown() {
            addResult('📋 عرض تفصيل الراتب المتوقع...', 'info');

            try {
                const testEmployee = employees.find(emp => emp.id === TEST_EMPLOYEE_ID);
                if (!testEmployee) {
                    addResult('❌ الموظف التجريبي غير موجود', 'error');
                    return;
                }

                const workDays = employeeDaysData.find(record => 
                    parseInt(record.employeeId) === TEST_EMPLOYEE_ID
                );

                if (!workDays) {
                    addResult('❌ بيانات أيام العمل غير موجودة', 'error');
                    return;
                }

                // حساب التفاصيل
                const basicSalary = testEmployee.salary.basic;
                const fixedAllowances = testEmployee.salary.allowances.total;
                const dailyAllowances = (testEmployee.salary.dailyAllowances.expatDaily + 
                                       testEmployee.salary.dailyAllowances.transportDaily) * workDays.actualDays;
                const overtimePay = workDays.overtimeHours * 25;
                const incentives = 1500;
                const rewards = 1000;
                const advances = 2000;
                const deductions = 500;

                const grossSalary = basicSalary + fixedAllowances + dailyAllowances + overtimePay + incentives + rewards;
                const netSalary = grossSalary - advances - deductions;

                addResult(`💼 الراتب الأساسي: ${basicSalary.toLocaleString()} ج.م`, 'info');
                addResult(`🏠 البدلات الثابتة: ${fixedAllowances.toLocaleString()} ج.م`, 'info');
                addResult(`🌍🚗 البدلات اليومية: ${dailyAllowances.toLocaleString()} ج.م`, 'info');
                addResult(`⏰ الساعات الإضافية: ${overtimePay.toLocaleString()} ج.م`, 'info');
                addResult(`🏆 الحوافز والمكافآت: ${(incentives + rewards).toLocaleString()} ج.م`, 'info');
                addResult(`💸 السلف والخصومات: ${(advances + deductions).toLocaleString()} ج.م`, 'warning');
                addResult(`💰 إجمالي الراتب: ${grossSalary.toLocaleString()} ج.م`, 'success');
                addResult(`🎯 صافي الراتب: ${netSalary.toLocaleString()} ج.م`, 'success');

            } catch (error) {
                addResult(`❌ خطأ في عرض تفصيل الراتب: ${error.message}`, 'error');
            }
        }

        // مسح النتائج
        function clearResults() {
            document.getElementById('results').innerHTML = '';
            addResult('تم مسح النتائج', 'info');
        }

        // تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            addResult('🚀 تم تحميل صفحة اختبار الراتب الشامل', 'info');
            addResult('جاهز لبدء الاختبارات الشاملة...', 'info');

            // اختبار سريع تلقائي
            setTimeout(() => {
                addResult('--- اختبار تلقائي سريع ---', 'info');
                if (typeof openSalaryCalculator === 'function') {
                    addResult('✅ دالة حساب الراتب متاحة', 'success');
                } else {
                    addResult('❌ دالة حساب الراتب غير متاحة', 'error');
                }
            }, 500);
        });
    </script>
</body>
</html>
