<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار نافذة حساب الراتب البسيطة الجديدة</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
            direction: rtl;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #4CAF50, #45a049);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 24px;
            margin-bottom: 10px;
        }

        .header p {
            opacity: 0.9;
            font-size: 16px;
        }

        .content {
            padding: 30px;
        }

        .test-section {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            border-left: 4px solid #4CAF50;
        }

        .test-section h2 {
            color: #333;
            margin-bottom: 15px;
            font-size: 18px;
        }

        .button-group {
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
            justify-content: center;
            margin-bottom: 20px;
        }

        .test-button {
            background: linear-gradient(135deg, #4CAF50, #45a049);
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s ease;
            min-width: 150px;
        }

        .test-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(76, 175, 80, 0.4);
        }

        .test-button.secondary {
            background: linear-gradient(135deg, #2196F3, #1976D2);
        }

        .test-button.warning {
            background: linear-gradient(135deg, #FF9800, #F57C00);
        }

        .test-button.danger {
            background: linear-gradient(135deg, #f44336, #d32f2f);
        }

        .results {
            background: white;
            border-radius: 10px;
            border: 1px solid #e0e0e0;
            max-height: 400px;
            overflow-y: auto;
        }

        .result-item {
            padding: 12px 15px;
            border-bottom: 1px solid #f0f0f0;
            font-size: 14px;
        }

        .result-item:last-child {
            border-bottom: none;
        }

        .result-item.success {
            background: #f0fff4;
            color: #2e7d32;
            border-left: 4px solid #4CAF50;
        }

        .result-item.error {
            background: #fff5f5;
            color: #c62828;
            border-left: 4px solid #f44336;
        }

        .result-item.info {
            background: #f3f8ff;
            color: #1565c0;
            border-left: 4px solid #2196F3;
        }

        .result-item.warning {
            background: #fffbf0;
            color: #ef6c00;
            border-left: 4px solid #FF9800;
        }

        .status-bar {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            text-align: center;
            font-weight: 500;
        }

        .status-bar.ready {
            background: #f0fff4;
            color: #2e7d32;
            border: 1px solid #4CAF50;
        }

        .status-bar.testing {
            background: #fff3e0;
            color: #ef6c00;
            border: 1px solid #FF9800;
        }

        .status-bar.error {
            background: #fff5f5;
            color: #c62828;
            border: 1px solid #f44336;
        }

        .feature-list {
            background: white;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            border: 1px solid #e0e0e0;
        }

        .feature-list h3 {
            color: #333;
            margin-bottom: 15px;
            font-size: 16px;
        }

        .feature-list ul {
            list-style: none;
            padding: 0;
        }

        .feature-list li {
            padding: 8px 0;
            color: #666;
            font-size: 14px;
        }

        .feature-list li:before {
            content: "✅ ";
            color: #4CAF50;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🧪 اختبار نافذة حساب الراتب البسيطة الجديدة</h1>
            <p>اختبار شامل للنافذة الجديدة مع البدلات المحسنة</p>
        </div>

        <div class="content">
            <div class="status-bar ready" id="statusBar">
                🚀 جاهز للاختبار - اضغط على أي زر لبدء الاختبار
            </div>

            <div class="feature-list">
                <h3>🎨 مميزات النافذة الجديدة:</h3>
                <ul>
                    <li>تصميم بسيط ونظيف</li>
                    <li>عرض واضح لأيام العمل والغياب</li>
                    <li>حساب دقيق للبدلات اليومية</li>
                    <li>عرض مفصل لجميع مكونات الراتب</li>
                    <li>واجهة مستخدم عصرية ومتجاوبة</li>
                    <li>أزرار تفاعلية للإجراءات</li>
                </ul>
            </div>

            <div class="test-section">
                <h2>🧪 اختبارات النافذة الجديدة</h2>
                <div class="button-group">
                    <button class="test-button" onclick="testNewWindow()">
                        🚀 اختبار النافذة الجديدة
                    </button>
                    <button class="test-button secondary" onclick="testWithExistingEmployee()">
                        👤 اختبار مع موظف موجود
                    </button>
                    <button class="test-button warning" onclick="testAllowancesCalculation()">
                        💰 اختبار حساب البدلات
                    </button>
                    <button class="test-button danger" onclick="clearAllTests()">
                        🗑️ مسح جميع الاختبارات
                    </button>
                </div>
            </div>

            <div class="test-section">
                <h2>📊 نتائج الاختبارات</h2>
                <div class="results" id="results">
                    <div class="result-item info">
                        💡 اضغط على أي زر أعلاه لبدء الاختبارات
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- تضمين ملف JavaScript الرئيسي -->
    <script src="app.js"></script>

    <script>
        // دالة لإضافة نتيجة اختبار
        function addResult(message, type = 'info') {
            const results = document.getElementById('results');
            const resultItem = document.createElement('div');
            resultItem.className = `result-item ${type}`;
            resultItem.textContent = `${new Date().toLocaleTimeString()} - ${message}`;
            
            // إضافة النتيجة في الأعلى
            results.insertBefore(resultItem, results.firstChild);
            
            // الحد الأقصى للنتائج
            const maxResults = 50;
            while (results.children.length > maxResults) {
                results.removeChild(results.lastChild);
            }
            
            // التمرير للأعلى
            results.scrollTop = 0;
        }

        // دالة لتحديث شريط الحالة
        function updateStatus(message, type = 'ready') {
            const statusBar = document.getElementById('statusBar');
            statusBar.textContent = message;
            statusBar.className = `status-bar ${type}`;
        }

        // اختبار النافذة الجديدة
        function testNewWindow() {
            addResult('🚀 بدء اختبار النافذة الجديدة...', 'info');
            updateStatus('🧪 جاري الاختبار...', 'testing');

            try {
                // التحقق من وجود الدالة
                if (typeof testNewSalaryWindow === 'function') {
                    addResult('✅ دالة testNewSalaryWindow موجودة', 'success');
                    
                    // تشغيل الاختبار
                    testNewSalaryWindow();
                    addResult('✅ تم تشغيل اختبار النافذة الجديدة', 'success');
                    updateStatus('✅ تم فتح النافذة الجديدة بنجاح', 'ready');
                } else {
                    addResult('❌ دالة testNewSalaryWindow غير موجودة', 'error');
                    updateStatus('❌ فشل في الاختبار', 'error');
                }
            } catch (error) {
                addResult(`❌ خطأ في الاختبار: ${error.message}`, 'error');
                updateStatus('❌ خطأ في الاختبار', 'error');
            }
        }

        // اختبار مع موظف موجود
        function testWithExistingEmployee() {
            addResult('👤 اختبار مع موظف موجود...', 'info');
            updateStatus('🧪 جاري الاختبار مع موظف موجود...', 'testing');

            try {
                // التحقق من وجود موظفين
                if (typeof employees !== 'undefined' && employees.length > 0) {
                    const firstEmployee = employees[0];
                    addResult(`✅ تم العثور على موظف: ${firstEmployee.name}`, 'success');
                    
                    // فتح النافذة للموظف الأول
                    if (typeof openSalaryCalculator === 'function') {
                        openSalaryCalculator(firstEmployee.id);
                        addResult('✅ تم فتح النافذة للموظف الموجود', 'success');
                        updateStatus('✅ تم فتح النافذة للموظف الموجود', 'ready');
                    } else {
                        addResult('❌ دالة openSalaryCalculator غير موجودة', 'error');
                        updateStatus('❌ فشل في فتح النافذة', 'error');
                    }
                } else {
                    addResult('⚠️ لا يوجد موظفين في النظام', 'warning');
                    addResult('💡 سيتم إنشاء موظف تجريبي...', 'info');
                    testNewWindow();
                }
            } catch (error) {
                addResult(`❌ خطأ في الاختبار: ${error.message}`, 'error');
                updateStatus('❌ خطأ في الاختبار', 'error');
            }
        }

        // اختبار حساب البدلات
        function testAllowancesCalculation() {
            addResult('💰 اختبار حساب البدلات...', 'info');
            updateStatus('🧪 جاري اختبار حساب البدلات...', 'testing');

            try {
                // تطبيق إصلاح البدلات
                if (typeof instantFixAllowances === 'function') {
                    const result = instantFixAllowances();
                    addResult('✅ تم تطبيق إصلاح البدلات', 'success');
                    
                    // فتح النافذة الجديدة
                    setTimeout(() => {
                        testNewWindow();
                        addResult('💡 تحقق من البدلات في النافذة المفتوحة', 'info');
                        updateStatus('✅ تم اختبار حساب البدلات', 'ready');
                    }, 1000);
                } else {
                    addResult('❌ دالة instantFixAllowances غير موجودة', 'error');
                    updateStatus('❌ فشل في اختبار البدلات', 'error');
                }
            } catch (error) {
                addResult(`❌ خطأ في اختبار البدلات: ${error.message}`, 'error');
                updateStatus('❌ خطأ في اختبار البدلات', 'error');
            }
        }

        // مسح جميع الاختبارات
        function clearAllTests() {
            const results = document.getElementById('results');
            results.innerHTML = '<div class="result-item info">💡 تم مسح جميع النتائج - اضغط على أي زر لبدء اختبار جديد</div>';
            updateStatus('🚀 جاهز للاختبار - اضغط على أي زر لبدء الاختبار', 'ready');
            addResult('🗑️ تم مسح جميع نتائج الاختبارات', 'info');
        }

        // رسالة ترحيب عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            addResult('🎉 مرحباً بك في نظام اختبار النافذة الجديدة!', 'success');
            addResult('💡 اضغط على "اختبار النافذة الجديدة" لبدء الاختبار', 'info');
        });
    </script>
</body>
</html>
