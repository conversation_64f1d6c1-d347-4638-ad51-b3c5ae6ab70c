<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار حساب الراتب مع الحوافز والمكافآت</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            direction: rtl;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            margin: 0;
            font-size: 28px;
            font-weight: 700;
        }

        .header p {
            margin: 10px 0 0 0;
            opacity: 0.9;
            font-size: 16px;
        }

        .content {
            padding: 30px;
        }

        .test-section {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 25px;
            margin-bottom: 25px;
            border: 1px solid #e9ecef;
        }

        .test-section h2 {
            margin: 0 0 20px 0;
            color: #495057;
            font-size: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .test-button {
            background: linear-gradient(135deg, #007bff, #0056b3);
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 600;
            margin: 5px;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .test-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,123,255,0.3);
        }

        .test-button.success {
            background: linear-gradient(135deg, #28a745, #20c997);
        }

        .test-button.success:hover {
            box-shadow: 0 5px 15px rgba(40,167,69,0.3);
        }

        .test-button.warning {
            background: linear-gradient(135deg, #ffc107, #fd7e14);
        }

        .test-button.warning:hover {
            box-shadow: 0 5px 15px rgba(255,193,7,0.3);
        }

        .test-button.danger {
            background: linear-gradient(135deg, #dc3545, #c82333);
        }

        .test-button.danger:hover {
            box-shadow: 0 5px 15px rgba(220,53,69,0.3);
        }

        .results-container {
            background: white;
            border-radius: 10px;
            padding: 20px;
            margin-top: 20px;
            border: 1px solid #e9ecef;
            max-height: 400px;
            overflow-y: auto;
        }

        .results-container h3 {
            margin: 0 0 15px 0;
            color: #495057;
            font-size: 18px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .test-result {
            padding: 8px 12px;
            margin: 5px 0;
            border-radius: 6px;
            font-size: 14px;
            border-right: 4px solid;
        }

        .test-result.success {
            background: #d4edda;
            color: #155724;
            border-color: #28a745;
        }

        .test-result.error {
            background: #f8d7da;
            color: #721c24;
            border-color: #dc3545;
        }

        .test-result.warning {
            background: #fff3cd;
            color: #856404;
            border-color: #ffc107;
        }

        .test-result.info {
            background: #d1ecf1;
            color: #0c5460;
            border-color: #17a2b8;
        }

        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }

        .status-card {
            background: white;
            border-radius: 8px;
            padding: 15px;
            border: 1px solid #e9ecef;
            text-align: center;
        }

        .status-card.success {
            border-color: #28a745;
            background: linear-gradient(135deg, #d4edda, #c3e6cb);
        }

        .status-card.error {
            border-color: #dc3545;
            background: linear-gradient(135deg, #f8d7da, #f5c6cb);
        }

        .status-card h4 {
            margin: 0 0 10px 0;
            font-size: 16px;
        }

        .status-card p {
            margin: 0;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="fas fa-calculator"></i> اختبار حساب الراتب مع الحوافز والمكافآت</h1>
            <p>اختبار شامل لنظام حساب الراتب المحدث مع تضمين الحوافز والمكافآت</p>
        </div>

        <div class="content">
            <!-- اختبارات إنشاء البيانات -->
            <div class="test-section">
                <h2><i class="fas fa-database"></i> إنشاء البيانات التجريبية</h2>

                <div style="margin-bottom: 15px;">
                    <button class="test-button success" onclick="createTestEmployee()">
                        <i class="fas fa-user-plus"></i> إنشاء موظف تجريبي
                    </button>
                    <button class="test-button success" onclick="createTestIncentives()">
                        <i class="fas fa-gift"></i> إنشاء حوافز ومكافآت
                    </button>
                    <button class="test-button success" onclick="createTestAdvances()">
                        <i class="fas fa-money-bill"></i> إنشاء سلف وخصومات
                    </button>
                    <button class="test-button success" onclick="createTestDays()">
                        <i class="fas fa-calendar"></i> إنشاء أيام عمل
                    </button>
                </div>

                <div>
                    <button class="test-button warning" onclick="createAllTestData()">
                        <i class="fas fa-magic"></i> إنشاء جميع البيانات التجريبية
                    </button>
                    <button class="test-button danger" onclick="clearAllTestData()">
                        <i class="fas fa-trash"></i> مسح جميع البيانات
                    </button>
                </div>
            </div>

            <!-- اختبارات حساب الراتب -->
            <div class="test-section">
                <h2><i class="fas fa-calculator"></i> اختبار حساب الراتب</h2>

                <div style="margin-bottom: 15px;">
                    <button class="test-button" onclick="testSalaryCalculation()">
                        <i class="fas fa-play"></i> اختبار حساب الراتب
                    </button>
                    <button class="test-button" onclick="openSalaryCalculatorTest()">
                        <i class="fas fa-window-maximize"></i> فتح نافذة حساب الراتب
                    </button>
                    <button class="test-button" onclick="testIncentivesInSalary()">
                        <i class="fas fa-check-circle"></i> اختبار تضمين الحوافز
                    </button>
                </div>

                <div>
                    <button class="test-button warning" onclick="runAllSalaryTests()">
                        <i class="fas fa-cogs"></i> تشغيل جميع اختبارات الراتب
                    </button>
                    <button class="test-button success" onclick="openOriginalApp()">
                        <i class="fas fa-external-link-alt"></i> فتح التطبيق الأصلي
                    </button>
                </div>
            </div>

            <!-- نتائج الاختبارات -->
            <div class="results-container">
                <h3><i class="fas fa-clipboard-list"></i> نتائج الاختبارات</h3>
                <div id="test-results">
                    <div class="test-result info">🚀 جاهز لبدء اختبار حساب الراتب مع الحوافز والمكافآت...</div>
                </div>
            </div>

            <!-- حالة النظام -->
            <div class="status-grid">
                <div class="status-card" id="data-status">
                    <h4>حالة البيانات</h4>
                    <p>⏳ في انتظار إنشاء البيانات...</p>
                </div>
                <div class="status-card" id="calculation-status">
                    <h4>حالة الحسابات</h4>
                    <p>⏳ في انتظار اختبار الحسابات...</p>
                </div>
                <div class="status-card" id="integration-status">
                    <h4>حالة التكامل</h4>
                    <p>⏳ في انتظار اختبار التكامل...</p>
                </div>
            </div>
        </div>
    </div>

    <!-- تضمين ملف JavaScript الأصلي -->
    <script src="app.js"></script>

    <script>
        // دالة لإضافة نتيجة اختبار
        function addResult(message, type = 'info') {
            const resultsContainer = document.getElementById('test-results');
            const resultDiv = document.createElement('div');
            resultDiv.className = `test-result ${type}`;
            resultDiv.textContent = message;
            resultsContainer.appendChild(resultDiv);
            resultsContainer.scrollTop = resultsContainer.scrollHeight;
        }

        // دالة لتحديث حالة البطاقات
        function updateStatus(cardId, success, message) {
            const card = document.getElementById(cardId);
            if (card) {
                card.className = `status-card ${success ? 'success' : 'error'}`;
                card.querySelector('p').textContent = message;
            }
        }

        // دالة لإنشاء موظف تجريبي
        function createTestEmployee() {
            addResult('إنشاء موظف تجريبي...', 'info');

            const testEmployee = {
                id: 9999,
                name: 'أحمد محمد علي',
                position: 'مطور برمجيات',
                employeeCode: 'TEST001',
                nationalId: '12345678901234',
                phone: '01234567890',
                employmentType: 'monthly',
                basicSalary: 8000,
                photo: 'https://randomuser.me/api/portraits/men/45.jpg'
            };

            // إضافة الموظف إلى القائمة
            const existingIndex = employees.findIndex(emp => emp.id === 9999);
            if (existingIndex !== -1) {
                employees[existingIndex] = testEmployee;
                addResult('تم تحديث الموظف التجريبي الموجود', 'warning');
            } else {
                employees.push(testEmployee);
                addResult('تم إنشاء موظف تجريبي جديد', 'success');
            }

            // حفظ البيانات
            saveEmployeesToLocalStorage();
            addResult(`✅ تم حفظ الموظف: ${testEmployee.name}`, 'success');
            updateStatus('data-status', true, '✅ تم إنشاء الموظف التجريبي');
        }

        // دالة لإنشاء حوافز ومكافآت تجريبية
        function createTestIncentives() {
            addResult('إنشاء حوافز ومكافآت تجريبية...', 'info');

            const testIncentives = [
                {
                    id: Date.now() + 1,
                    employeeId: 9999,
                    type: 'incentive',
                    month: 11,
                    year: 2024,
                    amount: 1500,
                    description: 'حافز الأداء المتميز',
                    date: new Date().toISOString()
                },
                {
                    id: Date.now() + 2,
                    employeeId: 9999,
                    type: 'reward',
                    month: 11,
                    year: 2024,
                    amount: 2000,
                    description: 'مكافأة إنجاز المشروع',
                    date: new Date().toISOString()
                }
            ];

            // إضافة الحوافز والمكافآت
            testIncentives.forEach(incentive => {
                incentivesRewardsData.push(incentive);
                const typeText = incentive.type === 'incentive' ? 'حافز' : 'مكافأة';
                addResult(`✅ تم إضافة ${typeText}: ${incentive.amount} ج.م`, 'success');
            });

            // حفظ البيانات
            saveIncentivesRewardsToLocalStorage();
            addResult('✅ تم حفظ جميع الحوافز والمكافآت', 'success');
        }

        // دالة لإنشاء سلف وخصومات تجريبية
        function createTestAdvances() {
            addResult('إنشاء سلف وخصومات تجريبية...', 'info');

            const testAdvances = [
                {
                    id: Date.now() + 3,
                    employeeId: 9999,
                    type: 'advance',
                    month: 11,
                    year: 2024,
                    amount: 1000,
                    description: 'سلفة شخصية',
                    date: new Date().toISOString()
                },
                {
                    id: Date.now() + 4,
                    employeeId: 9999,
                    type: 'deduction',
                    month: 11,
                    year: 2024,
                    amount: 500,
                    description: 'خصم تأخير',
                    date: new Date().toISOString()
                }
            ];

            // إضافة السلف والخصومات
            testAdvances.forEach(advance => {
                advancesDeductionsData.push(advance);
                const typeText = advance.type === 'advance' ? 'سلفة' : 'خصم';
                addResult(`✅ تم إضافة ${typeText}: ${advance.amount} ج.م`, 'success');
            });

            // حفظ البيانات
            saveAdvancesDeductionsToLocalStorage();
            addResult('✅ تم حفظ جميع السلف والخصومات', 'success');
        }

        // دالة لإنشاء أيام عمل تجريبية
        function createTestDays() {
            addResult('إنشاء أيام عمل تجريبية...', 'info');

            const testDays = {
                id: Date.now() + 5,
                employeeId: 9999,
                month: 11,
                year: 2024,
                calculatedDays: 25,
                absenceDays: 3,
                actualDays: 22,
                overtimeHours: 15,
                projectId: 1,
                date: new Date().toISOString()
            };

            // إضافة أيام العمل
            employeeDaysData.push(testDays);
            addResult(`✅ تم إضافة أيام العمل: ${testDays.actualDays} يوم فعلي`, 'success');
            addResult(`✅ ساعات إضافية: ${testDays.overtimeHours} ساعة`, 'success');

            // حفظ البيانات
            saveDaysDataToLocalStorage();
            addResult('✅ تم حفظ بيانات أيام العمل', 'success');
        }

        // دالة لإنشاء جميع البيانات التجريبية
        function createAllTestData() {
            addResult('🚀 بدء إنشاء جميع البيانات التجريبية...', 'info');

            createTestEmployee();
            setTimeout(() => {
                createTestIncentives();
                setTimeout(() => {
                    createTestAdvances();
                    setTimeout(() => {
                        createTestDays();
                        addResult('🎉 تم إنشاء جميع البيانات التجريبية بنجاح!', 'success');
                        updateStatus('data-status', true, '✅ جميع البيانات جاهزة');
                    }, 500);
                }, 500);
            }, 500);
        }

        // دالة لمسح جميع البيانات التجريبية
        function clearAllTestData() {
            if (!confirm('هل أنت متأكد من مسح جميع البيانات التجريبية؟')) {
                return;
            }

            addResult('🗑️ مسح جميع البيانات التجريبية...', 'warning');

            // مسح الموظف التجريبي
            const employeeIndex = employees.findIndex(emp => emp.id === 9999);
            if (employeeIndex !== -1) {
                employees.splice(employeeIndex, 1);
                addResult('✅ تم مسح الموظف التجريبي', 'success');
            }

            // مسح الحوافز والمكافآت
            incentivesRewardsData = incentivesRewardsData.filter(item => item.employeeId !== 9999);
            addResult('✅ تم مسح الحوافز والمكافآت التجريبية', 'success');

            // مسح السلف والخصومات
            advancesDeductionsData = advancesDeductionsData.filter(item => item.employeeId !== 9999);
            addResult('✅ تم مسح السلف والخصومات التجريبية', 'success');

            // مسح أيام العمل
            employeeDaysData = employeeDaysData.filter(item => item.employeeId !== 9999);
            addResult('✅ تم مسح أيام العمل التجريبية', 'success');

            // حفظ البيانات المحدثة
            saveEmployeesToLocalStorage();
            saveIncentivesRewardsToLocalStorage();
            saveAdvancesDeductionsToLocalStorage();
            saveDaysDataToLocalStorage();

            addResult('🎉 تم مسح جميع البيانات التجريبية بنجاح!', 'success');
            updateStatus('data-status', false, '⚠️ تم مسح البيانات');
            updateStatus('calculation-status', false, '⏳ في انتظار البيانات');
            updateStatus('integration-status', false, '⏳ في انتظار البيانات');
        }

        // دالة لاختبار حساب الراتب
        function testSalaryCalculation() {
            addResult('🧮 اختبار حساب الراتب...', 'info');

            // البحث عن الموظف التجريبي
            const employee = employees.find(emp => emp.id === 9999);
            if (!employee) {
                addResult('❌ لم يتم العثور على الموظف التجريبي', 'error');
                updateStatus('calculation-status', false, '❌ الموظف غير موجود');
                return;
            }

            // حساب الحوافز والمكافآت
            const incentives = incentivesRewardsData.filter(item =>
                item.employeeId === 9999 && item.month === 11 && item.year === 2024
            );

            const totalIncentives = incentives
                .filter(item => item.type === 'incentive')
                .reduce((sum, item) => sum + item.amount, 0);

            const totalRewards = incentives
                .filter(item => item.type === 'reward')
                .reduce((sum, item) => sum + item.amount, 0);

            addResult(`💰 إجمالي الحوافز: ${totalIncentives} ج.م`, 'success');
            addResult(`🎁 إجمالي المكافآت: ${totalRewards} ج.م`, 'success');
            addResult(`✨ إجمالي الحوافز والمكافآت: ${totalIncentives + totalRewards} ج.م`, 'success');

            // حساب السلف والخصومات
            const advances = advancesDeductionsData.filter(item =>
                item.employeeId === 9999 && item.month === 11 && item.year === 2024
            );

            const totalAdvances = advances
                .filter(item => item.type === 'advance')
                .reduce((sum, item) => sum + item.amount, 0);

            const totalDeductions = advances
                .filter(item => item.type === 'deduction')
                .reduce((sum, item) => sum + item.amount, 0);

            addResult(`💸 إجمالي السلف: ${totalAdvances} ج.م`, 'warning');
            addResult(`📉 إجمالي الخصومات: ${totalDeductions} ج.م`, 'warning');
            addResult(`⚠️ إجمالي السلف والخصومات: ${totalAdvances + totalDeductions} ج.م`, 'warning');

            // حساب الراتب النهائي
            const basicSalary = 8000; // الراتب الأساسي
            const overtimePay = 15 * (basicSalary / 30 / 8); // 15 ساعة إضافية
            const finalSalary = basicSalary + overtimePay + totalIncentives + totalRewards - totalAdvances - totalDeductions;

            addResult(`💼 الراتب الأساسي: ${basicSalary} ج.م`, 'info');
            addResult(`⏰ أجر الساعات الإضافية: ${overtimePay.toFixed(2)} ج.م`, 'info');
            addResult(`🏆 صافي الراتب المستحق: ${finalSalary.toFixed(2)} ج.م`, 'success');

            updateStatus('calculation-status', true, '✅ تم حساب الراتب بنجاح');
        }

        // دالة لفتح نافذة حساب الراتب التجريبية
        function openSalaryCalculatorTest() {
            addResult('🖥️ فتح نافذة حساب الراتب...', 'info');

            // البحث عن الموظف التجريبي
            const employee = employees.find(emp => emp.id === 9999);
            if (!employee) {
                addResult('❌ لم يتم العثور على الموظف التجريبي', 'error');
                return;
            }

            // فتح نافذة حساب الراتب
            try {
                openSalaryCalculator(9999);
                addResult('✅ تم فتح نافذة حساب الراتب بنجاح', 'success');
                updateStatus('integration-status', true, '✅ النافذة تعمل بشكل صحيح');
            } catch (error) {
                addResult(`❌ خطأ في فتح نافذة حساب الراتب: ${error.message}`, 'error');
                updateStatus('integration-status', false, '❌ خطأ في النافذة');
            }
        }

        // دالة لاختبار تضمين الحوافز في الراتب
        function testIncentivesInSalary() {
            addResult('🔍 اختبار تضمين الحوافز في حساب الراتب...', 'info');

            // التحقق من وجود البيانات
            const employee = employees.find(emp => emp.id === 9999);
            if (!employee) {
                addResult('❌ لم يتم العثور على الموظف التجريبي', 'error');
                return;
            }

            const incentives = incentivesRewardsData.filter(item => item.employeeId === 9999);
            if (incentives.length === 0) {
                addResult('❌ لا توجد حوافز أو مكافآت للموظف التجريبي', 'error');
                return;
            }

            // اختبار دالة حساب الحوافز والمكافآت
            const currentMonth = 11;
            const currentYear = 2024;

            const monthlyIncentives = incentivesRewardsData.filter(record =>
                parseInt(record.employeeId) === 9999 &&
                parseInt(record.month) === currentMonth &&
                parseInt(record.year) === currentYear
            );

            const totalIncentives = monthlyIncentives
                .filter(record => record.type === 'incentive')
                .reduce((sum, record) => sum + parseFloat(record.amount), 0);

            const totalRewards = monthlyIncentives
                .filter(record => record.type === 'reward')
                .reduce((sum, record) => sum + parseFloat(record.amount), 0);

            addResult(`✅ تم العثور على ${monthlyIncentives.length} سجل حوافز/مكافآت`, 'success');
            addResult(`✅ إجمالي الحوافز: ${totalIncentives} ج.م`, 'success');
            addResult(`✅ إجمالي المكافآت: ${totalRewards} ج.م`, 'success');
            addResult(`✅ إجمالي الحوافز والمكافآت: ${totalIncentives + totalRewards} ج.م`, 'success');

            // التحقق من أن الحوافز ستظهر في حساب الراتب
            if (totalIncentives + totalRewards > 0) {
                addResult('✅ الحوافز والمكافآت ستظهر في حساب الراتب', 'success');
                updateStatus('integration-status', true, '✅ التكامل يعمل بشكل صحيح');
            } else {
                addResult('⚠️ لا توجد حوافز أو مكافآت لهذا الشهر', 'warning');
            }
        }

        // دالة لتشغيل جميع اختبارات الراتب
        function runAllSalaryTests() {
            addResult('🚀 تشغيل جميع اختبارات الراتب...', 'info');

            testSalaryCalculation();
            setTimeout(() => {
                testIncentivesInSalary();
                setTimeout(() => {
                    addResult('🎉 تم الانتهاء من جميع اختبارات الراتب!', 'success');
                }, 1000);
            }, 1000);
        }

        // دالة لفتح التطبيق الأصلي
        function openOriginalApp() {
            addResult('🌐 فتح التطبيق الأصلي...', 'info');
            window.open('index.html', '_blank');
            addResult('✅ تم فتح التطبيق الأصلي في نافذة جديدة', 'success');
        }

        // تشغيل اختبار تلقائي عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            addResult('🚀 مرحباً بك في نظام اختبار حساب الراتب مع الحوافز والمكافآت', 'info');
            addResult('📋 يمكنك الآن إنشاء البيانات التجريبية واختبار النظام', 'info');

            // فحص سريع للنظام
            setTimeout(() => {
                addResult('🔍 فحص سريع للنظام...', 'info');

                // فحص المتغيرات العامة
                if (typeof employees !== 'undefined') {
                    addResult('✅ متغير employees: موجود', 'success');
                } else {
                    addResult('❌ متغير employees: مفقود', 'error');
                }

                if (typeof incentivesRewardsData !== 'undefined') {
                    addResult('✅ متغير incentivesRewardsData: موجود', 'success');
                } else {
                    addResult('❌ متغير incentivesRewardsData: مفقود', 'error');
                }

                if (typeof advancesDeductionsData !== 'undefined') {
                    addResult('✅ متغير advancesDeductionsData: موجود', 'success');
                } else {
                    addResult('❌ متغير advancesDeductionsData: مفقود', 'error');
                }

                if (typeof employeeDaysData !== 'undefined') {
                    addResult('✅ متغير employeeDaysData: موجود', 'success');
                } else {
                    addResult('❌ متغير employeeDaysData: مفقود', 'error');
                }

                // فحص الدوال الأساسية
                const criticalFunctions = [
                    'openSalaryCalculator',
                    'saveIncentivesRewardsToLocalStorage',
                    'saveAdvancesDeductionsToLocalStorage',
                    'saveDaysDataToLocalStorage'
                ];

                criticalFunctions.forEach(funcName => {
                    if (typeof window[funcName] === 'function') {
                        addResult(`✅ دالة ${funcName}: متاحة`, 'success');
                    } else {
                        addResult(`❌ دالة ${funcName}: غير متاحة`, 'error');
                    }
                });

                addResult('✅ انتهى الفحص السريع للنظام', 'success');
                addResult('🎯 النظام جاهز للاختبار!', 'success');
            }, 1000);
        });
    </script>
</body>
</html>
