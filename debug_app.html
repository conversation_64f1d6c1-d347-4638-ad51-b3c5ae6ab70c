<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تشخيص مشكلة البرنامج</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
            direction: rtl;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        
        .header {
            text-align: center;
            background: #dc3545;
            color: white;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        
        .status {
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            border-left: 4px solid;
        }
        
        .status.success {
            background: #d4edda;
            border-left-color: #28a745;
            color: #155724;
        }
        
        .status.error {
            background: #f8d7da;
            border-left-color: #dc3545;
            color: #721c24;
        }
        
        .status.warning {
            background: #fff3cd;
            border-left-color: #ffc107;
            color: #856404;
        }
        
        .console {
            background: #212529;
            color: #00ff00;
            padding: 15px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            font-size: 13px;
            max-height: 400px;
            overflow-y: auto;
            margin: 15px 0;
        }
        
        .button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        
        .button:hover {
            background: #0056b3;
        }
        
        .button.danger {
            background: #dc3545;
        }
        
        .button.danger:hover {
            background: #c82333;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔧 تشخيص مشكلة البرنامج</h1>
            <p>فحص شامل لمعرفة سبب عدم عمل البرنامج</p>
        </div>

        <div style="text-align: center; margin: 20px 0;">
            <button class="button" onclick="checkJavaScriptErrors()">فحص أخطاء JavaScript</button>
            <button class="button" onclick="checkFileLoading()">فحص تحميل الملفات</button>
            <button class="button" onclick="checkBasicFunctions()">فحص الدوال الأساسية</button>
            <button class="button danger" onclick="runFullDiagnosis()">تشخيص شامل</button>
        </div>

        <div id="status" class="status warning">
            <strong>الحالة:</strong> جاهز لبدء التشخيص...
        </div>

        <div class="console" id="console">
            [DEBUG] نظام التشخيص جاهز...<br>
        </div>
    </div>

    <script>
        function updateStatus(message, type = 'warning') {
            const statusDiv = document.getElementById('status');
            statusDiv.className = `status ${type}`;
            statusDiv.innerHTML = `<strong>الحالة:</strong> ${message}`;
        }

        function addLog(message) {
            const console = document.getElementById('console');
            const time = new Date().toLocaleTimeString();
            console.innerHTML += `[${time}] ${message}<br>`;
            console.scrollTop = console.scrollHeight;
        }

        function checkJavaScriptErrors() {
            addLog('🔍 فحص أخطاء JavaScript...');
            
            // فحص وجود ملف app.js
            const scripts = document.querySelectorAll('script[src]');
            let appJsFound = false;
            
            scripts.forEach(script => {
                if (script.src.includes('app.js')) {
                    appJsFound = true;
                    addLog('✓ تم العثور على ملف app.js');
                }
            });
            
            if (!appJsFound) {
                addLog('❌ ملف app.js غير موجود أو غير محمل');
                updateStatus('ملف app.js غير محمل!', 'error');
                return;
            }
            
            // فحص الأخطاء في وحدة التحكم
            addLog('فحص الأخطاء في وحدة التحكم...');
            
            // محاولة تحميل ملف app.js
            fetch('app.js')
                .then(response => {
                    if (response.ok) {
                        addLog('✓ ملف app.js يمكن الوصول إليه');
                        return response.text();
                    } else {
                        throw new Error(`HTTP ${response.status}`);
                    }
                })
                .then(content => {
                    addLog(`✓ حجم ملف app.js: ${content.length} حرف`);
                    
                    // فحص بداية الملف
                    const firstLine = content.split('\n')[0];
                    addLog(`أول سطر في الملف: ${firstLine}`);
                    
                    updateStatus('ملف app.js محمل بنجاح', 'success');
                })
                .catch(error => {
                    addLog(`❌ خطأ في تحميل app.js: ${error.message}`);
                    updateStatus(`خطأ في تحميل app.js: ${error.message}`, 'error');
                });
        }

        function checkFileLoading() {
            addLog('🔍 فحص تحميل الملفات...');
            
            // فحص ملف index.html
            addLog('فحص ملف index.html...');
            if (document.title) {
                addLog(`✓ عنوان الصفحة: ${document.title}`);
            }
            
            // فحص العناصر الأساسية
            const requiredElements = [
                'searchBtn',
                'addEmployeeBtn', 
                'tableViewBtn',
                'employeesContainer'
            ];
            
            let elementsFound = 0;
            requiredElements.forEach(elementId => {
                const element = document.getElementById(elementId);
                if (element) {
                    addLog(`✓ عنصر موجود: ${elementId}`);
                    elementsFound++;
                } else {
                    addLog(`❌ عنصر مفقود: ${elementId}`);
                }
            });
            
            if (elementsFound === requiredElements.length) {
                updateStatus('جميع العناصر الأساسية موجودة', 'success');
            } else {
                updateStatus(`${elementsFound}/${requiredElements.length} عنصر موجود`, 'warning');
            }
        }

        function checkBasicFunctions() {
            addLog('🔍 فحص الدوال الأساسية...');
            
            // قائمة الدوال المطلوبة
            const requiredFunctions = [
                'populateEmployees',
                'openModal',
                'searchEmployees',
                'loadEmployeesFromLocalStorage'
            ];
            
            let functionsFound = 0;
            requiredFunctions.forEach(funcName => {
                if (typeof window[funcName] === 'function') {
                    addLog(`✓ دالة موجودة: ${funcName}`);
                    functionsFound++;
                } else {
                    addLog(`❌ دالة مفقودة: ${funcName}`);
                }
            });
            
            // فحص المتغيرات العامة
            const requiredVariables = ['employees', 'activeEmployees'];
            let variablesFound = 0;
            
            requiredVariables.forEach(varName => {
                if (typeof window[varName] !== 'undefined') {
                    addLog(`✓ متغير موجود: ${varName}`);
                    variablesFound++;
                } else {
                    addLog(`❌ متغير مفقود: ${varName}`);
                }
            });
            
            const totalRequired = requiredFunctions.length + requiredVariables.length;
            const totalFound = functionsFound + variablesFound;
            
            if (totalFound === totalRequired) {
                updateStatus('جميع الدوال والمتغيرات موجودة', 'success');
            } else {
                updateStatus(`${totalFound}/${totalRequired} دالة/متغير موجود`, 'warning');
            }
        }

        function runFullDiagnosis() {
            updateStatus('🚀 بدء التشخيص الشامل...', 'warning');
            addLog('=== بدء التشخيص الشامل ===');
            
            // مسح السجل
            setTimeout(() => {
                document.getElementById('console').innerHTML = '[FULL_DIAGNOSIS] بدء التشخيص الشامل...<br>';
            }, 500);
            
            // فحص الملفات
            setTimeout(() => {
                checkFileLoading();
            }, 1000);
            
            // فحص JavaScript
            setTimeout(() => {
                checkJavaScriptErrors();
            }, 3000);
            
            // فحص الدوال
            setTimeout(() => {
                checkBasicFunctions();
            }, 5000);
            
            // النتيجة النهائية
            setTimeout(() => {
                addLog('=== انتهاء التشخيص الشامل ===');
                addLog('📋 راجع النتائج أعلاه لمعرفة المشكلة');
                
                // محاولة تحديد المشكلة الرئيسية
                if (typeof window.employees === 'undefined') {
                    addLog('🎯 المشكلة المحتملة: ملف app.js لم يتم تحميله بشكل صحيح');
                    updateStatus('المشكلة: ملف app.js لم يتم تحميله', 'error');
                } else {
                    addLog('🎯 الملفات محملة - قد تكون المشكلة في الدوال');
                    updateStatus('الملفات محملة - فحص الدوال مطلوب', 'warning');
                }
            }, 7000);
        }

        // فحص فوري عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            addLog('تم تحميل صفحة التشخيص');
            
            // فحص سريع
            setTimeout(() => {
                if (typeof window.employees === 'undefined') {
                    addLog('⚠️ تحذير: متغير employees غير موجود');
                    updateStatus('تحذير: البرنامج الأساسي لم يتم تحميله', 'error');
                } else {
                    addLog('✓ متغير employees موجود');
                    updateStatus('البرنامج الأساسي محمل جزئياً', 'success');
                }
            }, 1000);
        });
    </script>
    
    <!-- محاولة تحميل ملف app.js -->
    <script src="app.js"></script>
</body>
</html>
