<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار النافذة المُنظفة لحساب الأيام</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            direction: rtl;
        }

        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 3px solid #667eea;
        }

        .header h1 {
            color: #333;
            margin: 0 0 10px 0;
            font-size: 28px;
        }

        .header p {
            color: #666;
            margin: 0;
            font-size: 16px;
        }

        .test-section {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            border-left: 5px solid #007bff;
        }

        .test-section h3 {
            margin: 0 0 15px 0;
            color: #007bff;
            font-size: 18px;
        }

        .btn {
            background: linear-gradient(135deg, #007bff, #0056b3);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 600;
            margin: 5px;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0, 123, 255, 0.3);
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0, 123, 255, 0.4);
        }

        .btn.success {
            background: linear-gradient(135deg, #28a745, #1e7e34);
            box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
        }

        .btn.warning {
            background: linear-gradient(135deg, #ffc107, #e0a800);
            box-shadow: 0 4px 15px rgba(255, 193, 7, 0.3);
        }

        .btn.danger {
            background: linear-gradient(135deg, #dc3545, #c82333);
            box-shadow: 0 4px 15px rgba(220, 53, 69, 0.3);
        }

        .status {
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
            font-weight: 600;
            text-align: center;
        }

        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }

        .status.warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }

        .log-container {
            background: #2c3e50;
            color: #ecf0f1;
            padding: 20px;
            border-radius: 10px;
            max-height: 400px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            line-height: 1.6;
            margin-top: 20px;
        }

        .log-entry {
            margin: 5px 0;
            padding: 5px 0;
            border-bottom: 1px solid #34495e;
        }

        .log-entry.success { color: #2ecc71; }
        .log-entry.error { color: #e74c3c; }
        .log-entry.warning { color: #f39c12; }
        .log-entry.info { color: #3498db; }

        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }

        .stat-card {
            background: white;
            padding: 15px;
            border-radius: 8px;
            text-align: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            border-top: 3px solid #007bff;
        }

        .stat-number {
            font-size: 24px;
            font-weight: bold;
            color: #007bff;
            margin-bottom: 5px;
        }

        .stat-label {
            color: #666;
            font-size: 12px;
            font-weight: 600;
        }

        .feature-list {
            background: #e8f5e8;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #28a745;
            margin: 15px 0;
        }

        .feature-list h4 {
            margin: 0 0 10px 0;
            color: #155724;
        }

        .feature-list ul {
            margin: 0;
            padding-right: 20px;
        }

        .feature-list li {
            margin: 5px 0;
            color: #155724;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🧹 اختبار النافذة المُنظفة لحساب الأيام</h1>
            <p>نافذة مُحسنة ومبسطة بدون أكواد مكررة مع تصميم نظيف وبسيط</p>
        </div>

        <div id="status" class="status info">
            <strong>الحالة:</strong> جاهز للاختبار
        </div>

        <div class="stats">
            <div class="stat-card">
                <div class="stat-number" id="testsRun">0</div>
                <div class="stat-label">اختبارات منفذة</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="testsSuccess">0</div>
                <div class="stat-label">نجحت</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="testsFailed">0</div>
                <div class="stat-label">فشلت</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="codeQuality">A+</div>
                <div class="stat-label">جودة الكود</div>
            </div>
        </div>

        <div class="feature-list">
            <h4>✨ الميزات الجديدة في النافذة المُنظفة:</h4>
            <ul>
                <li>تصميم بسيط ومجرد من الألوان المعقدة</li>
                <li>حذف جميع الدوال المكررة</li>
                <li>تحسين الأداء وتقليل حجم الكود</li>
                <li>واجهة مستخدم نظيفة وسهلة الاستخدام</li>
                <li>دعم كامل لجميع المشروعات</li>
                <li>حفظ البيانات في localStorage</li>
                <li>نظام تحقق محسن من البيانات</li>
            </ul>
        </div>

        <div class="test-section">
            <h3>🔍 إعداد البيانات التجريبية</h3>
            <button class="btn" onclick="createTestEmployee()">إنشاء موظف تجريبي</button>
            <button class="btn warning" onclick="createTestProjects()">إنشاء مشروعات تجريبية</button>
            <button class="btn danger" onclick="clearTestData()">مسح البيانات التجريبية</button>
        </div>

        <div class="test-section">
            <h3>🚀 اختبار النافذة المُنظفة</h3>
            <button class="btn success" onclick="testCleanWindow()">فتح النافذة المُنظفة</button>
            <button class="btn" onclick="testWindowElements()">فحص عناصر النافذة</button>
            <button class="btn" onclick="testProjectsLoading()">اختبار تحميل المشروعات</button>
            <button class="btn warning" onclick="testSaveFunction()">اختبار دالة الحفظ</button>
        </div>

        <div class="test-section">
            <h3>📋 اختبارات شاملة</h3>
            <button class="btn success" onclick="runFullTest()">تشغيل جميع الاختبارات</button>
            <button class="btn" onclick="testCodeQuality()">فحص جودة الكود</button>
            <button class="btn warning" onclick="performanceTest()">اختبار الأداء</button>
        </div>

        <div class="log-container" id="logContainer">
            <div class="log-entry info">[CLEAN_WINDOW_TEST] نظام اختبار النافذة المُنظفة جاهز...</div>
        </div>
    </div>

    <!-- تحميل ملف JavaScript الأساسي -->
    <script src="app.js"></script>

    <script>
        let testsRun = 0;
        let testsSuccess = 0;
        let testsFailed = 0;

        function updateStatus(message, type = 'info') {
            const statusDiv = document.getElementById('status');
            statusDiv.className = `status ${type}`;
            statusDiv.innerHTML = `<strong>الحالة:</strong> ${message}`;
        }

        function addLog(message, type = 'info') {
            const logContainer = document.getElementById('logContainer');
            const time = new Date().toLocaleTimeString('ar-EG');
            const logEntry = document.createElement('div');
            logEntry.className = `log-entry ${type}`;
            logEntry.innerHTML = `[${time}] ${message}`;
            logContainer.appendChild(logEntry);
            logContainer.scrollTop = logContainer.scrollHeight;
        }

        function updateStats() {
            document.getElementById('testsRun').textContent = testsRun;
            document.getElementById('testsSuccess').textContent = testsSuccess;
            document.getElementById('testsFailed').textContent = testsFailed;

            // حساب جودة الكود
            const successRate = testsRun > 0 ? (testsSuccess / testsRun) * 100 : 0;
            let grade = 'F';
            if (successRate >= 95) grade = 'A+';
            else if (successRate >= 90) grade = 'A';
            else if (successRate >= 85) grade = 'B+';
            else if (successRate >= 80) grade = 'B';
            else if (successRate >= 70) grade = 'C';
            else if (successRate >= 60) grade = 'D';

            document.getElementById('codeQuality').textContent = grade;
        }

        function runTest(testName, testFunction) {
            testsRun++;
            addLog(`🔧 بدء اختبار: ${testName}`, 'info');

            try {
                const result = testFunction();
                if (result !== false) {
                    testsSuccess++;
                    addLog(`✅ نجح اختبار: ${testName}`, 'success');
                } else {
                    testsFailed++;
                    addLog(`❌ فشل اختبار: ${testName}`, 'error');
                }
            } catch (error) {
                testsFailed++;
                addLog(`❌ خطأ في اختبار ${testName}: ${error.message}`, 'error');
            }

            updateStats();
        }

        function createTestEmployee() {
            addLog('📋 إنشاء موظف تجريبي للنافذة المُنظفة...', 'info');

            try {
                const testEmployee = {
                    id: 5001,
                    name: "موظف تجريبي - النافذة المُنظفة",
                    position: "مطور أول",
                    employeeCode: "CLEAN_5001",
                    employmentType: "monthly",
                    salary: { basic: 7000, total: 7000 }
                };

                if (typeof employees !== 'undefined') {
                    employees = employees.filter(emp => emp.id !== 5001);
                    employees.push(testEmployee);
                } else {
                    window.employees = [testEmployee];
                }

                addLog('✅ تم إنشاء الموظف التجريبي: ' + testEmployee.name, 'success');
                updateStatus('تم إنشاء الموظف التجريبي بنجاح', 'success');

            } catch (error) {
                addLog('❌ خطأ في إنشاء الموظف: ' + error.message, 'error');
                updateStatus('خطأ في إنشاء الموظف التجريبي', 'error');
            }
        }

        function createTestProjects() {
            addLog('🏗️ إنشاء مشروعات تجريبية للنافذة المُنظفة...', 'info');

            try {
                const testProjects = [
                    {
                        id: 'clean_proj_001',
                        name: 'مشروع النافذة المُنظفة 1',
                        description: 'مشروع تجريبي للاختبار',
                        status: 'in-progress',
                        startDate: '2024-01-01',
                        endDate: '2024-01-31'
                    },
                    {
                        id: 'clean_proj_002',
                        name: 'مشروع النافذة المُنظفة 2',
                        description: 'مشروع آخر للاختبار',
                        status: 'planning',
                        startDate: '2024-02-01',
                        endDate: '2024-02-28'
                    }
                ];

                if (typeof projects !== 'undefined') {
                    projects = projects.filter(p => !p.id.startsWith('clean_proj_'));
                    projects.push(...testProjects);
                } else {
                    window.projects = testProjects;
                }

                addLog(`✅ تم إنشاء ${testProjects.length} مشروع تجريبي`, 'success');
                updateStatus('تم إنشاء المشروعات التجريبية بنجاح', 'success');

            } catch (error) {
                addLog('❌ خطأ في إنشاء المشروعات: ' + error.message, 'error');
            }
        }

        function clearTestData() {
            if (confirm('هل أنت متأكد من مسح جميع البيانات التجريبية؟')) {
                // مسح الموظفين التجريبيين
                if (typeof employees !== 'undefined') {
                    employees = employees.filter(emp => emp.id !== 5001);
                }

                // مسح المشروعات التجريبية
                if (typeof projects !== 'undefined') {
                    projects = projects.filter(p => !p.id.startsWith('clean_proj_'));
                }

                // مسح سجلات الأيام
                if (typeof employeeDaysData !== 'undefined') {
                    employeeDaysData = employeeDaysData.filter(record => record.employeeId !== 5001);
                }

                // إغلاق النافذة
                const modal = document.getElementById('daysModal');
                if (modal) {
                    modal.remove();
                }

                // مسح السجل
                document.getElementById('logContainer').innerHTML =
                    '<div class="log-entry info">[CLEAN_WINDOW_TEST] تم مسح البيانات - النظام جاهز للاختبار...</div>';

                // إعادة تعيين الإحصائيات
                testsRun = 0;
                testsSuccess = 0;
                testsFailed = 0;
                updateStats();

                updateStatus('تم مسح جميع البيانات التجريبية', 'success');
            }
        }

        function testCleanWindow() {
            runTest('فتح النافذة المُنظفة', () => {
                if (!employees || !employees.find(emp => emp.id === 5001)) {
                    addLog('⚠️ يرجى إنشاء الموظف التجريبي أولاً', 'warning');
                    return false;
                }

                if (typeof openDaysCalculator !== 'function') {
                    addLog('❌ دالة openDaysCalculator غير موجودة', 'error');
                    return false;
                }

                openDaysCalculator(5001);

                setTimeout(() => {
                    const modal = document.getElementById('daysModal');
                    if (modal) {
                        addLog('✅ تم فتح النافذة المُنظفة بنجاح', 'success');
                        updateStatus('✅ النافذة المُنظفة مفتوحة - يمكنك الآن اختبار العناصر', 'success');
                    } else {
                        addLog('❌ لم يتم فتح النافذة', 'error');
                        updateStatus('❌ فشل في فتح النافذة', 'error');
                    }
                }, 500);

                return true;
            });
        }

        function testWindowElements() {
            runTest('فحص عناصر النافذة المُنظفة', () => {
                const modal = document.getElementById('daysModal');
                if (!modal) {
                    addLog('⚠️ يرجى فتح النافذة أولاً', 'warning');
                    return false;
                }

                const requiredElements = [
                    { id: 'projectSelect', name: 'قائمة المشروعات' },
                    { id: 'startDate', name: 'تاريخ البداية' },
                    { id: 'endDate', name: 'تاريخ النهاية' },
                    { id: 'calculatedDays', name: 'أيام العمل' },
                    { id: 'absenceDays', name: 'أيام الغياب' },
                    { id: 'overtimeHours', name: 'ساعات إضافية' },
                    { id: 'daysEmployeeId', name: 'معرف الموظف المخفي' }
                ];

                let foundElements = 0;
                requiredElements.forEach(element => {
                    const el = document.getElementById(element.id);
                    if (el) {
                        foundElements++;
                        addLog(`✅ عنصر موجود: ${element.name}`, 'success');
                    } else {
                        addLog(`❌ عنصر مفقود: ${element.name}`, 'error');
                    }
                });

                if (foundElements === requiredElements.length) {
                    addLog(`🎉 جميع العناصر موجودة (${foundElements}/${requiredElements.length})`, 'success');
                    return true;
                } else {
                    addLog(`⚠️ بعض العناصر مفقودة (${foundElements}/${requiredElements.length})`, 'warning');
                    return false;
                }
            });
        }

        function testProjectsLoading() {
            runTest('اختبار تحميل المشروعات', () => {
                const projectSelect = document.getElementById('projectSelect');
                if (!projectSelect) {
                    addLog('❌ قائمة المشروعات غير موجودة', 'error');
                    return false;
                }

                const optionsCount = projectSelect.options.length;
                addLog(`📋 عدد خيارات المشروعات: ${optionsCount}`, 'info');

                if (optionsCount > 1) { // أكثر من الخيار الافتراضي
                    addLog('✅ قائمة المشروعات تحتوي على خيارات', 'success');

                    // اختبار وجود المشروعات التجريبية
                    let foundTestProjects = 0;
                    for (let i = 0; i < projectSelect.options.length; i++) {
                        const option = projectSelect.options[i];
                        if (option.textContent.includes('النافذة المُنظفة')) {
                            foundTestProjects++;
                        }
                    }

                    if (foundTestProjects > 0) {
                        addLog(`✅ تم العثور على ${foundTestProjects} مشروع تجريبي`, 'success');
                        return true;
                    } else {
                        addLog('⚠️ لم يتم العثور على مشروعات تجريبية', 'warning');
                        return false;
                    }
                } else {
                    addLog('⚠️ قائمة المشروعات فارغة', 'warning');
                    return false;
                }
            });
        }

        function testSaveFunction() {
            runTest('اختبار دالة الحفظ', () => {
                if (typeof saveDaysCalculation === 'function') {
                    addLog('✅ دالة الحفظ موجودة', 'success');

                    // ملء بعض الحقول للاختبار
                    try {
                        const projectSelect = document.getElementById('projectSelect');
                        const calculatedDaysInput = document.getElementById('calculatedDays');

                        if (projectSelect && projectSelect.options.length > 1) {
                            projectSelect.selectedIndex = 1; // اختيار أول مشروع
                            addLog('✅ تم اختيار مشروع للاختبار', 'success');
                        }

                        if (calculatedDaysInput) {
                            calculatedDaysInput.value = '25';
                            addLog('✅ تم ملء حقل أيام العمل', 'success');
                        }

                        return true;
                    } catch (error) {
                        addLog('❌ خطأ في اختبار دالة الحفظ: ' + error.message, 'error');
                        return false;
                    }
                } else {
                    addLog('❌ دالة الحفظ غير موجودة', 'error');
                    return false;
                }
            });
        }

        function testCodeQuality() {
            runTest('فحص جودة الكود', () => {
                addLog('🔍 فحص جودة الكود...', 'info');

                // فحص وجود الدوال الأساسية
                const requiredFunctions = [
                    'openDaysCalculator',
                    'closeDaysModal',
                    'loadProjectsIntoDropdown',
                    'saveDaysCalculation'
                ];

                let functionsFound = 0;
                requiredFunctions.forEach(funcName => {
                    if (typeof window[funcName] === 'function') {
                        functionsFound++;
                        addLog(`✅ دالة موجودة: ${funcName}`, 'success');
                    } else {
                        addLog(`❌ دالة مفقودة: ${funcName}`, 'error');
                    }
                });

                // فحص عدم وجود دوال مكررة
                const duplicateFunctions = [
                    'openDaysCalculationModal',
                    'createEnhancedDaysCalculator',
                    'populateProjectsDropdown'
                ];

                let duplicatesFound = 0;
                duplicateFunctions.forEach(funcName => {
                    if (typeof window[funcName] === 'function') {
                        duplicatesFound++;
                        addLog(`⚠️ دالة مكررة موجودة: ${funcName}`, 'warning');
                    } else {
                        addLog(`✅ دالة مكررة محذوفة: ${funcName}`, 'success');
                    }
                });

                const qualityScore = (functionsFound / requiredFunctions.length) * 100;
                const duplicateScore = duplicatesFound === 0 ? 100 : 50;
                const overallScore = (qualityScore + duplicateScore) / 2;

                addLog(`📊 نتيجة جودة الكود: ${overallScore.toFixed(1)}%`, 'info');

                return overallScore >= 80;
            });
        }

        function performanceTest() {
            runTest('اختبار الأداء', () => {
                addLog('⚡ اختبار أداء النافذة...', 'info');

                const startTime = performance.now();

                // اختبار سرعة فتح النافذة
                if (employees && employees.find(emp => emp.id === 5001)) {
                    openDaysCalculator(5001);

                    setTimeout(() => {
                        const endTime = performance.now();
                        const loadTime = endTime - startTime;

                        addLog(`⏱️ زمن فتح النافذة: ${loadTime.toFixed(2)} مللي ثانية`, 'info');

                        if (loadTime < 500) {
                            addLog('✅ أداء ممتاز - النافذة تفتح بسرعة', 'success');
                            return true;
                        } else if (loadTime < 1000) {
                            addLog('⚠️ أداء جيد - النافذة تفتح بسرعة مقبولة', 'warning');
                            return true;
                        } else {
                            addLog('❌ أداء بطيء - النافذة تستغرق وقت طويل', 'error');
                            return false;
                        }
                    }, 100);

                    return true;
                } else {
                    addLog('⚠️ لا يوجد موظف تجريبي لاختبار الأداء', 'warning');
                    return false;
                }
            });
        }

        function runFullTest() {
            addLog('🚀 تشغيل جميع الاختبارات...', 'info');
            updateStatus('تشغيل جميع الاختبارات...', 'info');

            // إعادة تعيين الإحصائيات
            testsRun = 0;
            testsSuccess = 0;
            testsFailed = 0;
            updateStats();

            // تشغيل الاختبارات بالتسلسل
            setTimeout(() => createTestEmployee(), 500);
            setTimeout(() => createTestProjects(), 1500);
            setTimeout(() => testCleanWindow(), 2500);
            setTimeout(() => testWindowElements(), 4000);
            setTimeout(() => testProjectsLoading(), 5500);
            setTimeout(() => testSaveFunction(), 7000);
            setTimeout(() => testCodeQuality(), 8500);
            setTimeout(() => performanceTest(), 10000);

            setTimeout(() => {
                if (testsFailed === 0) {
                    updateStatus('🎉 جميع الاختبارات نجحت! النافذة المُنظفة تعمل بشكل مثالي', 'success');
                    addLog('🎉 تم اجتياز جميع الاختبارات بنجاح!', 'success');
                } else {
                    updateStatus(`⚠️ ${testsFailed} اختبار فشل من أصل ${testsRun}`, 'error');
                    addLog(`⚠️ فشل ${testsFailed} اختبار من أصل ${testsRun}`, 'warning');
                }
            }, 12000);
        }
    </script>
</body>
</html>
