<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار نهائي للأزرار</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            direction: rtl;
            background-color: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .button-test {
            display: flex;
            align-items: center;
            margin: 10px 0;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .button-test button {
            margin-left: 10px;
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            color: white;
        }
        .add-btn { background-color: #4CAF50; }
        .table-view-btn { background-color: #673AB7; }
        .import-btn { background-color: #2196F3; }
        .remove-duplicates-btn { background-color: #9C27B0; }
        .search-btn { background-color: #f0f0f0; color: black; }
        .status {
            padding: 5px 10px;
            border-radius: 3px;
            margin-right: 10px;
            font-weight: bold;
        }
        .success { background-color: #d4edda; color: #155724; }
        .error { background-color: #f8d7da; color: #721c24; }
        .pending { background-color: #fff3cd; color: #856404; }
        #results {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin-top: 20px;
            max-height: 300px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <h1>اختبار نهائي لأزرار نظام إدارة الموارد البشرية</h1>
    
    <div class="test-container">
        <h2>اختبار الأزرار الأساسية</h2>
        <p>انقر على كل زر لاختبار وظيفته:</p>
        
        <div class="button-test">
            <button class="search-btn" onclick="testSearchButton()">🔍 بحث</button>
            <span id="search-status" class="status pending">في انتظار الاختبار</span>
            <span>زر البحث</span>
        </div>
        
        <div class="button-test">
            <button class="add-btn" onclick="testAddButton()">+ موظف جديد</button>
            <span id="add-status" class="status pending">في انتظار الاختبار</span>
            <span>زر إضافة موظف</span>
        </div>
        
        <div class="button-test">
            <button class="table-view-btn" onclick="testTableButton()">📊 عرض جدول</button>
            <span id="table-status" class="status pending">في انتظار الاختبار</span>
            <span>زر عرض الجدول</span>
        </div>
        
        <div class="button-test">
            <button class="import-btn" onclick="testImportButton()">📥 استيراد من إكسل</button>
            <span id="import-status" class="status pending">في انتظار الاختبار</span>
            <span>زر استيراد من إكسل</span>
        </div>
        
        <div class="button-test">
            <button class="remove-duplicates-btn" onclick="testRemoveDuplicatesButton()">🗑️ إزالة المكررين</button>
            <span id="duplicates-status" class="status pending">في انتظار الاختبار</span>
            <span>زر إزالة المكررين</span>
        </div>
    </div>
    
    <div class="test-container">
        <h2>اختبار شامل</h2>
        <button onclick="runAllTests()" style="background-color: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer;">
            تشغيل جميع الاختبارات
        </button>
        <button onclick="openOriginalPage()" style="background-color: #28a745; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; margin-right: 10px;">
            فتح الصفحة الأصلية
        </button>
    </div>
    
    <div id="results">
        <h3>نتائج الاختبارات:</h3>
        <p>انقر على "تشغيل جميع الاختبارات" أو اختبر كل زر بشكل منفصل</p>
    </div>

    <script>
        function updateStatus(elementId, success, message) {
            const element = document.getElementById(elementId);
            if (success) {
                element.className = 'status success';
                element.textContent = '✓ يعمل';
            } else {
                element.className = 'status error';
                element.textContent = '✗ لا يعمل';
            }
            
            addResult(message);
        }
        
        function addResult(message) {
            const results = document.getElementById('results');
            const p = document.createElement('p');
            p.textContent = new Date().toLocaleTimeString() + ' - ' + message;
            results.appendChild(p);
            results.scrollTop = results.scrollHeight;
        }

        function testSearchButton() {
            try {
                // محاكاة وظيفة البحث
                addResult('اختبار زر البحث...');
                
                // التحقق من وجود دالة البحث
                if (typeof searchEmployees === 'function') {
                    updateStatus('search-status', true, 'زر البحث: دالة searchEmployees موجودة ويمكن استدعاؤها');
                } else {
                    updateStatus('search-status', false, 'زر البحث: دالة searchEmployees غير موجودة');
                }
            } catch (error) {
                updateStatus('search-status', false, 'زر البحث: خطأ - ' + error.message);
            }
        }

        function testAddButton() {
            try {
                addResult('اختبار زر إضافة موظف...');
                
                if (typeof openModal === 'function') {
                    updateStatus('add-status', true, 'زر إضافة موظف: دالة openModal موجودة ويمكن استدعاؤها');
                } else {
                    updateStatus('add-status', false, 'زر إضافة موظف: دالة openModal غير موجودة');
                }
            } catch (error) {
                updateStatus('add-status', false, 'زر إضافة موظف: خطأ - ' + error.message);
            }
        }

        function testTableButton() {
            try {
                addResult('اختبار زر عرض الجدول...');
                
                if (typeof openTableModal === 'function') {
                    updateStatus('table-status', true, 'زر عرض الجدول: دالة openTableModal موجودة ويمكن استدعاؤها');
                } else {
                    updateStatus('table-status', false, 'زر عرض الجدول: دالة openTableModal غير موجودة');
                }
            } catch (error) {
                updateStatus('table-status', false, 'زر عرض الجدول: خطأ - ' + error.message);
            }
        }

        function testImportButton() {
            try {
                addResult('اختبار زر استيراد من إكسل...');
                
                if (typeof importFromExcel === 'function') {
                    updateStatus('import-status', true, 'زر استيراد من إكسل: دالة importFromExcel موجودة ويمكن استدعاؤها');
                } else {
                    updateStatus('import-status', false, 'زر استيراد من إكسل: دالة importFromExcel غير موجودة');
                }
            } catch (error) {
                updateStatus('import-status', false, 'زر استيراد من إكسل: خطأ - ' + error.message);
            }
        }

        function testRemoveDuplicatesButton() {
            try {
                addResult('اختبار زر إزالة المكررين...');
                
                if (typeof removeDuplicateEmployees === 'function') {
                    updateStatus('duplicates-status', true, 'زر إزالة المكررين: دالة removeDuplicateEmployees موجودة ويمكن استدعاؤها');
                } else {
                    updateStatus('duplicates-status', false, 'زر إزالة المكررين: دالة removeDuplicateEmployees غير موجودة');
                }
            } catch (error) {
                updateStatus('duplicates-status', false, 'زر إزالة المكررين: خطأ - ' + error.message);
            }
        }

        function runAllTests() {
            addResult('=== بدء تشغيل جميع الاختبارات ===');
            
            setTimeout(() => testSearchButton(), 100);
            setTimeout(() => testAddButton(), 200);
            setTimeout(() => testTableButton(), 300);
            setTimeout(() => testImportButton(), 400);
            setTimeout(() => testRemoveDuplicatesButton(), 500);
            
            setTimeout(() => {
                addResult('=== انتهاء جميع الاختبارات ===');
            }, 600);
        }

        function openOriginalPage() {
            window.open('/', '_blank');
        }

        // تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            addResult('تم تحميل صفحة الاختبار النهائي');
            addResult('جاهز لبدء الاختبارات...');
        });
    </script>
</body>
</html>
