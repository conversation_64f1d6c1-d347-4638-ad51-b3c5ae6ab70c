<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>حاسبة الراتب المصغرة</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }
        
        .main-container {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            max-width: 800px;
            width: 100%;
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
            color: #333;
        }
        
        .employee-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 30px;
        }
        
        .employee-card {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .employee-card:hover {
            background: #e9ecef;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }
        
        .employee-name {
            font-weight: bold;
            color: #2196F3;
            margin-bottom: 5px;
        }
        
        .employee-info {
            font-size: 12px;
            color: #666;
            margin-bottom: 10px;
        }
        
        .calc-btn {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 8px 15px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
            width: 100%;
        }
        
        .calc-btn:hover {
            background: #45a049;
        }
        
        /* نافذة الراتب المصغرة */
        .salary-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
            display: none;
            align-items: center;
            justify-content: center;
            z-index: 10000;
        }
        
        .salary-window {
            background: white;
            border-radius: 8px;
            width: 350px;
            max-height: 80vh;
            overflow-y: auto;
            box-shadow: 0 4px 20px rgba(0,0,0,0.3);
            border: 2px solid #2196F3;
        }
        
        .salary-header {
            background: #2196F3;
            color: white;
            padding: 10px 15px;
            border-radius: 6px 6px 0 0;
            text-align: center;
            font-size: 12px;
            position: relative;
        }
        
        .close-btn {
            position: absolute;
            top: 8px;
            left: 12px;
            background: none;
            border: none;
            color: white;
            font-size: 16px;
            cursor: pointer;
            width: 20px;
            height: 20px;
        }
        
        .employee-details {
            background: #f9f9f9;
            padding: 8px 15px;
            border-bottom: 1px solid #eee;
            text-align: center;
            font-size: 10px;
            color: #666;
        }
        
        .salary-content {
            padding: 15px;
        }
        
        .salary-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 15px;
            font-size: 11px;
        }
        
        .salary-table th,
        .salary-table td {
            padding: 6px;
            border: 1px solid #ddd;
            text-align: center;
        }
        
        .salary-table th {
            background: #f0f0f0;
            font-weight: bold;
        }
        
        .salary-table .item-name {
            text-align: right;
        }
        
        .amount-positive {
            color: #4CAF50;
            font-weight: bold;
        }
        
        .amount-negative {
            color: #f44336;
            font-weight: bold;
        }
        
        .amount-neutral {
            color: #2196F3;
            font-weight: bold;
        }
        
        .total-row {
            background: #e8f5e9;
            font-weight: bold;
        }
        
        .total-amount {
            color: #2e7d32;
            font-size: 14px;
        }
        
        .action-buttons {
            text-align: center;
            margin-top: 15px;
        }
        
        .action-btn {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 6px 12px;
            margin: 0 2px;
            border-radius: 3px;
            cursor: pointer;
            font-size: 10px;
        }
        
        .action-btn.print {
            background: #2196F3;
        }
        
        .action-btn.close {
            background: #757575;
        }
        
        .action-btn:hover {
            opacity: 0.8;
        }
    </style>
</head>
<body>
    <div class="main-container">
        <div class="header">
            <h1>🧮 حاسبة الراتب المصغرة</h1>
            <p>اختر موظف لحساب راتبه</p>
        </div>
        
        <div class="employee-grid" id="employeeGrid">
            <!-- سيتم إضافة الموظفين هنا -->
        </div>
        
        <div style="text-align: center; margin-top: 20px;">
            <button onclick="addNewEmployee()" style="background: #2196F3; color: white; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer;">
                ➕ إضافة موظف جديد
            </button>
        </div>
    </div>
    
    <!-- نافذة حساب الراتب -->
    <div class="salary-modal" id="salaryModal">
        <div class="salary-window">
            <div class="salary-header">
                <span id="salaryTitle">حساب الراتب</span>
                <button class="close-btn" onclick="closeSalaryModal()">×</button>
            </div>
            
            <div class="employee-details" id="employeeDetails">
                معلومات الموظف
            </div>
            
            <div class="salary-content">
                <table class="salary-table">
                    <thead>
                        <tr>
                            <th class="item-name">البيان</th>
                            <th>المبلغ</th>
                        </tr>
                    </thead>
                    <tbody id="salaryTableBody">
                        <!-- سيتم إضافة بيانات الراتب هنا -->
                    </tbody>
                </table>
                
                <div class="action-buttons">
                    <button class="action-btn" onclick="saveSalary()">حفظ</button>
                    <button class="action-btn print" onclick="printSalary()">طباعة</button>
                    <button class="action-btn close" onclick="closeSalaryModal()">إغلاق</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // بيانات الموظفين التجريبية
        let employees = [
            {
                id: 1,
                name: 'أحمد محمد',
                position: 'مطور',
                type: 'monthly',
                basicSalary: 8000,
                allowances: 1200,
                incentives: 500,
                deductions: 200
            },
            {
                id: 2,
                name: 'فاطمة علي',
                position: 'محاسبة',
                type: 'monthly',
                basicSalary: 6500,
                allowances: 800,
                incentives: 300,
                deductions: 150
            },
            {
                id: 3,
                name: 'محمد حسن',
                position: 'عامل',
                type: 'daily',
                dailyWage: 200,
                workDays: 26,
                allowances: 400,
                incentives: 200,
                deductions: 100
            },
            {
                id: 4,
                name: 'سارة أحمد',
                position: 'سكرتيرة',
                type: 'monthly',
                basicSalary: 5000,
                allowances: 600,
                incentives: 250,
                deductions: 100
            }
        ];

        // عرض الموظفين
        function displayEmployees() {
            const grid = document.getElementById('employeeGrid');
            grid.innerHTML = '';
            
            employees.forEach(emp => {
                const card = document.createElement('div');
                card.className = 'employee-card';
                card.innerHTML = `
                    <div class="employee-name">${emp.name}</div>
                    <div class="employee-info">
                        كود: ${emp.id} | ${emp.position}<br>
                        ${emp.type === 'daily' ? 'يومي' : 'شهري'}
                    </div>
                    <button class="calc-btn" onclick="openSalaryCalculator(${emp.id})">
                        🧮 حساب الراتب
                    </button>
                `;
                grid.appendChild(card);
            });
        }

        // فتح نافذة حساب الراتب
        function openSalaryCalculator(employeeId) {
            const employee = employees.find(emp => emp.id === employeeId);
            if (!employee) return;

            // تحديث عنوان النافذة
            document.getElementById('salaryTitle').textContent = `راتب ${employee.name}`;
            
            // تحديث معلومات الموظف
            document.getElementById('employeeDetails').textContent = 
                `${employee.id} | ${employee.position} | ${employee.type === 'daily' ? 'يومي' : 'شهري'}`;

            // حساب الراتب
            let basicAmount = 0;
            let basicDescription = '';
            
            if (employee.type === 'daily') {
                basicAmount = employee.dailyWage * employee.workDays;
                basicDescription = `${employee.workDays} يوم × ${employee.dailyWage}`;
            } else {
                basicAmount = employee.basicSalary;
                basicDescription = 'راتب أساسي';
            }

            const totalBeforeDeductions = basicAmount + employee.allowances + employee.incentives;
            const netSalary = totalBeforeDeductions - employee.deductions;

            // تحديث جدول الراتب
            const tbody = document.getElementById('salaryTableBody');
            tbody.innerHTML = `
                <tr>
                    <td class="item-name">${basicDescription}</td>
                    <td class="amount-neutral">${basicAmount.toLocaleString()}</td>
                </tr>
                ${employee.allowances > 0 ? `
                <tr>
                    <td class="item-name">بدلات</td>
                    <td class="amount-positive">+${employee.allowances.toLocaleString()}</td>
                </tr>
                ` : ''}
                ${employee.incentives > 0 ? `
                <tr>
                    <td class="item-name">حوافز</td>
                    <td class="amount-positive">+${employee.incentives.toLocaleString()}</td>
                </tr>
                ` : ''}
                ${employee.deductions > 0 ? `
                <tr>
                    <td class="item-name">خصومات</td>
                    <td class="amount-negative">-${employee.deductions.toLocaleString()}</td>
                </tr>
                ` : ''}
                <tr class="total-row">
                    <td class="item-name">الصافي</td>
                    <td class="total-amount">${netSalary.toLocaleString()} ج.م</td>
                </tr>
            `;

            // إظهار النافذة
            document.getElementById('salaryModal').style.display = 'flex';
        }

        // إغلاق نافذة الراتب
        function closeSalaryModal() {
            document.getElementById('salaryModal').style.display = 'none';
        }

        // حفظ الراتب
        function saveSalary() {
            alert('✅ تم حفظ حساب الراتب بنجاح!');
        }

        // طباعة الراتب
        function printSalary() {
            window.print();
        }

        // إضافة موظف جديد
        function addNewEmployee() {
            const name = prompt('اسم الموظف:');
            if (!name) return;
            
            const position = prompt('الوظيفة:') || 'موظف';
            const type = confirm('هل الموظف يومي؟ (إلغاء = شهري)') ? 'daily' : 'monthly';
            
            const newEmployee = {
                id: employees.length + 1,
                name: name,
                position: position,
                type: type,
                basicSalary: type === 'monthly' ? 5000 : 0,
                dailyWage: type === 'daily' ? 150 : 0,
                workDays: type === 'daily' ? 26 : 0,
                allowances: 300,
                incentives: 200,
                deductions: 100
            };
            
            employees.push(newEmployee);
            displayEmployees();
            alert('✅ تم إضافة الموظف بنجاح!');
        }

        // إغلاق النافذة عند النقر خارجها
        document.getElementById('salaryModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeSalaryModal();
            }
        });

        // تحميل الموظفين عند بدء التشغيل
        displayEmployees();
    </script>
</body>
</html>
