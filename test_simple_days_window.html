<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار النافذة البسيطة لحساب الأيام</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
            direction: rtl;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 10px;
            padding: 30px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #007bff;
        }

        .header h1 {
            color: #333;
            margin: 0 0 10px 0;
            font-size: 24px;
        }

        .header p {
            color: #666;
            margin: 0;
            font-size: 14px;
        }

        .test-section {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            border-left: 4px solid #007bff;
        }

        .test-section h3 {
            margin: 0 0 15px 0;
            color: #007bff;
            font-size: 16px;
        }

        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            margin: 5px;
            transition: background-color 0.3s ease;
        }

        .btn:hover {
            background: #0056b3;
        }

        .btn.success {
            background: #28a745;
        }

        .btn.success:hover {
            background: #1e7e34;
        }

        .btn.warning {
            background: #ffc107;
            color: #333;
        }

        .btn.warning:hover {
            background: #e0a800;
        }

        .status {
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
            font-weight: 600;
            text-align: center;
        }

        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }

        .log-container {
            background: #2c3e50;
            color: #ecf0f1;
            padding: 15px;
            border-radius: 8px;
            max-height: 300px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 13px;
            line-height: 1.5;
            margin-top: 20px;
        }

        .log-entry {
            margin: 3px 0;
            padding: 3px 0;
        }

        .log-entry.success { color: #2ecc71; }
        .log-entry.error { color: #e74c3c; }
        .log-entry.warning { color: #f39c12; }
        .log-entry.info { color: #3498db; }

        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }

        .stat-card {
            background: white;
            padding: 15px;
            border-radius: 8px;
            text-align: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            border-top: 3px solid #007bff;
        }

        .stat-number {
            font-size: 24px;
            font-weight: bold;
            color: #007bff;
            margin-bottom: 5px;
        }

        .stat-label {
            color: #666;
            font-size: 12px;
            font-weight: 600;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔧 اختبار النافذة البسيطة لحساب الأيام</h1>
            <p>نافذة بسيطة ومجردة من الألوان مع قوائم منسدلة للشهر والسنة والمشروعات وحقل إدخال أيام العمل</p>
        </div>

        <div id="status" class="status info">
            <strong>الحالة:</strong> جاهز للاختبار
        </div>

        <div class="stats">
            <div class="stat-card">
                <div class="stat-number" id="testsRun">0</div>
                <div class="stat-label">اختبارات منفذة</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="testsSuccess">0</div>
                <div class="stat-label">نجحت</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="testsFailed">0</div>
                <div class="stat-label">فشلت</div>
            </div>
        </div>

        <div class="test-section">
            <h3>🔍 إعداد البيانات التجريبية</h3>
            <button class="btn" onclick="createTestData()">إنشاء بيانات تجريبية</button>
            <button class="btn warning" onclick="createTestProjects()">إنشاء مشروعات تجريبية</button>
        </div>

        <div class="test-section">
            <h3>🚀 اختبار النافذة البسيطة</h3>
            <button class="btn success" onclick="testSimpleWindow()">فتح النافذة البسيطة</button>
            <button class="btn" onclick="testWindowElements()">فحص عناصر النافذة</button>
            <button class="btn warning" onclick="testSaveFunction()">اختبار دالة الحفظ</button>
        </div>

        <div class="test-section">
            <h3>📋 اختبارات متقدمة</h3>
            <button class="btn" onclick="testProjectsDropdown()">اختبار قائمة المشروعات</button>
            <button class="btn" onclick="testMonthYearDropdowns()">اختبار قوائم الشهر والسنة</button>
            <button class="btn success" onclick="runAllTests()">تشغيل جميع الاختبارات</button>
        </div>

        <div class="log-container" id="logContainer">
            <div class="log-entry info">[SIMPLE_WINDOW_TEST] نظام اختبار النافذة البسيطة جاهز...</div>
        </div>
    </div>

    <!-- تحميل ملف JavaScript الأساسي -->
    <script src="app.js"></script>

    <script>
        let testsRun = 0;
        let testsSuccess = 0;
        let testsFailed = 0;

        function updateStatus(message, type = 'info') {
            const statusDiv = document.getElementById('status');
            statusDiv.className = `status ${type}`;
            statusDiv.innerHTML = `<strong>الحالة:</strong> ${message}`;
        }

        function addLog(message, type = 'info') {
            const logContainer = document.getElementById('logContainer');
            const time = new Date().toLocaleTimeString('ar-EG');
            const logEntry = document.createElement('div');
            logEntry.className = `log-entry ${type}`;
            logEntry.innerHTML = `[${time}] ${message}`;
            logContainer.appendChild(logEntry);
            logContainer.scrollTop = logContainer.scrollHeight;
        }

        function updateStats() {
            document.getElementById('testsRun').textContent = testsRun;
            document.getElementById('testsSuccess').textContent = testsSuccess;
            document.getElementById('testsFailed').textContent = testsFailed;
        }

        function runTest(testName, testFunction) {
            testsRun++;
            addLog(`🔧 بدء اختبار: ${testName}`, 'info');

            try {
                const result = testFunction();
                if (result !== false) {
                    testsSuccess++;
                    addLog(`✅ نجح اختبار: ${testName}`, 'success');
                } else {
                    testsFailed++;
                    addLog(`❌ فشل اختبار: ${testName}`, 'error');
                }
            } catch (error) {
                testsFailed++;
                addLog(`❌ خطأ في اختبار ${testName}: ${error.message}`, 'error');
            }

            updateStats();
        }

        function createTestData() {
            addLog('📋 إنشاء بيانات تجريبية...', 'info');

            try {
                // إنشاء موظف تجريبي
                const testEmployee = {
                    id: 3001,
                    name: "موظف تجريبي - النافذة البسيطة",
                    position: "مطور",
                    employeeCode: "SIMPLE_3001",
                    employmentType: "monthly",
                    salary: { basic: 5000, total: 5000 }
                };

                // إضافة الموظف
                if (typeof employees !== 'undefined') {
                    employees = employees.filter(emp => emp.id !== 3001);
                    employees.push(testEmployee);

                    addLog('✅ تم إنشاء موظف تجريبي: ' + testEmployee.name, 'success');
                    updateStatus('تم إنشاء البيانات التجريبية بنجاح', 'success');
                } else {
                    window.employees = [testEmployee];
                    addLog('✅ تم إنشاء قائمة موظفين جديدة', 'success');
                }

            } catch (error) {
                addLog('❌ خطأ في إنشاء البيانات: ' + error.message, 'error');
                updateStatus('خطأ في إنشاء البيانات التجريبية', 'error');
            }
        }

        function createTestProjects() {
            addLog('🏗️ إنشاء مشروعات تجريبية...', 'info');

            try {
                const testProjects = [
                    {
                        id: 'proj_001',
                        name: 'مشروع تجريبي 1',
                        description: 'مشروع للاختبار',
                        status: 'in-progress'
                    },
                    {
                        id: 'proj_002',
                        name: 'مشروع تجريبي 2',
                        description: 'مشروع آخر للاختبار',
                        status: 'planning'
                    }
                ];

                if (typeof projects !== 'undefined') {
                    projects = projects.filter(p => !p.id.startsWith('proj_'));
                    projects.push(...testProjects);
                } else {
                    window.projects = testProjects;
                }

                addLog(`✅ تم إنشاء ${testProjects.length} مشروع تجريبي`, 'success');
                updateStatus('تم إنشاء المشروعات التجريبية بنجاح', 'success');

            } catch (error) {
                addLog('❌ خطأ في إنشاء المشروعات: ' + error.message, 'error');
            }
        }

        function testSimpleWindow() {
            runTest('فتح النافذة البسيطة', () => {
                if (!employees || !employees.find(emp => emp.id === 3001)) {
                    addLog('⚠️ يرجى إنشاء البيانات التجريبية أولاً', 'warning');
                    return false;
                }

                if (typeof openDaysCalculator === 'function') {
                    openDaysCalculator(3001);

                    setTimeout(() => {
                        const modal = document.getElementById('daysModal');
                        if (modal) {
                            addLog('✅ تم فتح النافذة البسيطة بنجاح', 'success');
                            updateStatus('✅ النافذة البسيطة مفتوحة', 'success');
                        } else {
                            addLog('❌ لم يتم فتح النافذة', 'error');
                            updateStatus('❌ فشل في فتح النافذة', 'error');
                        }
                    }, 500);

                    return true;
                } else {
                    addLog('❌ دالة openDaysCalculator غير موجودة', 'error');
                    return false;
                }
            });
        }

        function testWindowElements() {
            runTest('فحص عناصر النافذة', () => {
                const modal = document.getElementById('daysModal');
                if (!modal) {
                    addLog('⚠️ يرجى فتح النافذة أولاً', 'warning');
                    return false;
                }

                const requiredElements = [
                    'daysMonth',
                    'daysYear',
                    'daysProject',
                    'calculatedDays',
                    'daysEmployeeId'
                ];

                let foundElements = 0;
                requiredElements.forEach(elementId => {
                    const element = document.getElementById(elementId);
                    if (element) {
                        foundElements++;
                        addLog(`✅ عنصر موجود: ${elementId}`, 'success');
                    } else {
                        addLog(`❌ عنصر مفقود: ${elementId}`, 'error');
                    }
                });

                if (foundElements === requiredElements.length) {
                    addLog(`✅ جميع العناصر موجودة (${foundElements}/${requiredElements.length})`, 'success');
                    return true;
                } else {
                    addLog(`⚠️ بعض العناصر مفقودة (${foundElements}/${requiredElements.length})`, 'warning');
                    return false;
                }
            });
        }

        function testProjectsDropdown() {
            runTest('اختبار قائمة المشروعات', () => {
                const projectSelect = document.getElementById('daysProject');
                if (!projectSelect) {
                    addLog('❌ قائمة المشروعات غير موجودة', 'error');
                    return false;
                }

                const optionsCount = projectSelect.options.length;
                addLog(`📋 عدد خيارات المشروعات: ${optionsCount}`, 'info');

                if (optionsCount > 1) { // أكثر من الخيار الافتراضي
                    addLog('✅ قائمة المشروعات تحتوي على خيارات', 'success');
                    return true;
                } else {
                    addLog('⚠️ قائمة المشروعات فارغة', 'warning');
                    return false;
                }
            });
        }

        function testMonthYearDropdowns() {
            runTest('اختبار قوائم الشهر والسنة', () => {
                const monthSelect = document.getElementById('daysMonth');
                const yearSelect = document.getElementById('daysYear');

                if (!monthSelect || !yearSelect) {
                    addLog('❌ قوائم الشهر أو السنة غير موجودة', 'error');
                    return false;
                }

                const monthOptions = monthSelect.options.length;
                const yearOptions = yearSelect.options.length;

                addLog(`📅 خيارات الشهر: ${monthOptions}`, 'info');
                addLog(`📅 خيارات السنة: ${yearOptions}`, 'info');

                if (monthOptions >= 12 && yearOptions >= 3) {
                    addLog('✅ قوائم الشهر والسنة تحتوي على خيارات كافية', 'success');
                    return true;
                } else {
                    addLog('⚠️ قوائم الشهر أو السنة لا تحتوي على خيارات كافية', 'warning');
                    return false;
                }
            });
        }

        function testSaveFunction() {
            runTest('اختبار دالة الحفظ', () => {
                if (typeof saveDaysCalculation === 'function') {
                    addLog('✅ دالة الحفظ موجودة', 'success');

                    // اختبار الحفظ مع بيانات ناقصة
                    try {
                        // ملء بعض الحقول
                        const monthSelect = document.getElementById('daysMonth');
                        const yearSelect = document.getElementById('daysYear');
                        const daysInput = document.getElementById('calculatedDays');

                        if (monthSelect) monthSelect.value = '12';
                        if (yearSelect) yearSelect.value = '2024';
                        if (daysInput) daysInput.value = '25';

                        addLog('📝 تم ملء بعض الحقول للاختبار', 'info');
                        return true;
                    } catch (error) {
                        addLog('❌ خطأ في اختبار دالة الحفظ: ' + error.message, 'error');
                        return false;
                    }
                } else {
                    addLog('❌ دالة الحفظ غير موجودة', 'error');
                    return false;
                }
            });
        }

        function runAllTests() {
            addLog('🚀 تشغيل جميع الاختبارات...', 'info');
            updateStatus('تشغيل جميع الاختبارات...', 'info');

            // إعادة تعيين الإحصائيات
            testsRun = 0;
            testsSuccess = 0;
            testsFailed = 0;
            updateStats();

            // تشغيل الاختبارات بالتسلسل
            setTimeout(() => createTestData(), 500);
            setTimeout(() => createTestProjects(), 1000);
            setTimeout(() => testSimpleWindow(), 2000);
            setTimeout(() => testWindowElements(), 3000);
            setTimeout(() => testProjectsDropdown(), 4000);
            setTimeout(() => testMonthYearDropdowns(), 5000);
            setTimeout(() => testSaveFunction(), 6000);

            setTimeout(() => {
                if (testsFailed === 0) {
                    updateStatus('🎉 جميع الاختبارات نجحت!', 'success');
                    addLog('🎉 تم اجتياز جميع الاختبارات بنجاح!', 'success');
                } else {
                    updateStatus(`⚠️ ${testsFailed} اختبار فشل من أصل ${testsRun}`, 'error');
                    addLog(`⚠️ فشل ${testsFailed} اختبار من أصل ${testsRun}`, 'warning');
                }
            }, 7000);
        }
    </script>
</body>
</html>
