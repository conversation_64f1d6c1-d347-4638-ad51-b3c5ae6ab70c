<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار حقول الموظف الشهري</title>
    <link rel="stylesheet" href="styles.css">
    <style>
        .test-container {
            max-width: 1200px;
            margin: 20px auto;
            padding: 20px;
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .test-header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 10px;
        }

        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background: #f9f9f9;
        }

        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }

        .test-button:hover {
            background: #0056b3;
        }

        .test-button.success {
            background: #28a745;
        }

        .test-button.warning {
            background: #ffc107;
            color: #333;
        }

        .test-button.danger {
            background: #dc3545;
        }

        .log-container {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            margin: 20px 0;
            max-height: 400px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 12px;
        }

        .log-entry {
            margin: 5px 0;
            padding: 5px;
            border-radius: 3px;
        }

        .log-entry.info {
            background: #d1ecf1;
            color: #0c5460;
        }

        .log-entry.success {
            background: #d4edda;
            color: #155724;
        }

        .log-entry.warning {
            background: #fff3cd;
            color: #856404;
        }

        .log-entry.error {
            background: #f8d7da;
            color: #721c24;
        }

        .status-display {
            padding: 15px;
            margin: 20px 0;
            border-radius: 5px;
            font-weight: bold;
            text-align: center;
        }

        .employee-info {
            background: #e9ecef;
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
        }

        .field-test-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
            margin: 15px 0;
        }

        .field-test-item {
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background: white;
            text-align: center;
        }

        .field-enabled {
            border-color: #28a745;
            background: #d4edda;
        }

        .field-disabled {
            border-color: #dc3545;
            background: #f8d7da;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-header">
            <h1>🧪 اختبار حقول الموظف الشهري</h1>
            <p>اختبار تفعيل/تعطيل الحقول حسب نوع الموظف (شهري/يومي)</p>
        </div>

        <div class="test-section">
            <h3>📋 إعداد البيانات التجريبية</h3>
            <button class="test-button" onclick="createTestEmployees()">إنشاء موظفين تجريبيين</button>
            <button class="test-button" onclick="createTestProjects()">إنشاء مشروعات تجريبية</button>
            <button class="test-button success" onclick="setupAllTestData()">إعداد جميع البيانات</button>
        </div>

        <div class="test-section">
            <h3>🔧 اختبار الموظف الشهري</h3>
            <div class="employee-info">
                <strong>الموظف الشهري:</strong> أحمد محمد (ID: 1001)
            </div>
            <button class="test-button" onclick="testMonthlyEmployee()">فتح نافذة الموظف الشهري</button>
            <button class="test-button" onclick="checkMonthlyFields()">فحص تفعيل الحقول</button>
        </div>

        <div class="test-section">
            <h3>⚡ اختبار الموظف اليومي</h3>
            <div class="employee-info">
                <strong>الموظف اليومي:</strong> سارة أحمد (ID: 1002)
            </div>
            <button class="test-button" onclick="testDailyEmployee()">فتح نافذة الموظف اليومي</button>
            <button class="test-button" onclick="checkDailyFields()">فحص تعطيل الحقول</button>
        </div>

        <div class="test-section">
            <h3>📊 نتائج الاختبار</h3>
            <div id="fieldTestResults" class="field-test-grid">
                <!-- سيتم ملء النتائج هنا -->
            </div>
        </div>

        <div class="test-section">
            <h3>🔄 اختبارات متقدمة</h3>
            <button class="test-button warning" onclick="testFieldToggling()">اختبار تبديل نوع الموظف</button>
            <button class="test-button warning" onclick="testFieldValidation()">اختبار التحقق من الحقول</button>
            <button class="test-button danger" onclick="clearTestData()">مسح البيانات التجريبية</button>
        </div>

        <div class="status-display" id="statusDisplay">
            جاهز للاختبار
        </div>

        <div class="log-container" id="logContainer">
            <div class="log-entry info">📝 سجل الاختبارات - جاهز للبدء</div>
        </div>
    </div>

    <script src="app.js"></script>
    <script>
        let testsRun = 0;
        let testsSuccess = 0;
        let testsFailed = 0;

        function addLog(message, type = 'info') {
            const logContainer = document.getElementById('logContainer');
            const timestamp = new Date().toLocaleTimeString('ar-EG');
            const logEntry = document.createElement('div');
            logEntry.className = `log-entry ${type}`;
            logEntry.textContent = `[${timestamp}] ${message}`;
            logContainer.appendChild(logEntry);
            logContainer.scrollTop = logContainer.scrollHeight;
            console.log(`[${type.toUpperCase()}] ${message}`);
        }

        function updateStatus(message, type = 'info') {
            const statusDisplay = document.getElementById('statusDisplay');
            statusDisplay.textContent = message;
            statusDisplay.className = `status-display ${type}`;
        }

        function createTestEmployees() {
            addLog('🔧 إنشاء موظفين تجريبيين...', 'info');

            // موظف شهري
            const monthlyEmployee = {
                id: 1001,
                name: 'أحمد محمد',
                employmentType: 'monthly',
                basicSalary: 5000,
                position: 'مهندس',
                department: 'الهندسة',
                hireDate: '2023-01-01',
                isActive: true
            };

            // موظف يومي
            const dailyEmployee = {
                id: 1002,
                name: 'سارة أحمد',
                employmentType: 'daily',
                dailyWage: 150,
                position: 'فني',
                department: 'الإنتاج',
                hireDate: '2023-06-01',
                isActive: true
            };

            // إضافة الموظفين إلى النظام
            if (!employees.find(emp => emp.id === 1001)) {
                employees.push(monthlyEmployee);
                addLog('✅ تم إنشاء الموظف الشهري: أحمد محمد', 'success');
            }

            if (!employees.find(emp => emp.id === 1002)) {
                employees.push(dailyEmployee);
                addLog('✅ تم إنشاء الموظف اليومي: سارة أحمد', 'success');
            }

            // حفظ في localStorage
            saveEmployeesToLocalStorage();
            populateEmployees();

            addLog('💾 تم حفظ الموظفين في النظام', 'success');
            updateStatus('تم إنشاء الموظفين التجريبيين بنجاح', 'success');
        }

        function createTestProjects() {
            addLog('🔧 إنشاء مشروعات تجريبية...', 'info');

            const testProjects = [
                {
                    id: 101,
                    name: 'مشروع تجريبي 1',
                    description: 'مشروع للاختبار',
                    status: 'active',
                    createdAt: new Date().toISOString()
                },
                {
                    id: 102,
                    name: 'مشروع تجريبي 2',
                    description: 'مشروع آخر للاختبار',
                    status: 'active',
                    createdAt: new Date().toISOString()
                }
            ];

            testProjects.forEach(project => {
                if (!projects.find(p => p.id === project.id)) {
                    projects.push(project);
                    addLog(`✅ تم إنشاء المشروع: ${project.name}`, 'success');
                }
            });

            saveProjectsToLocalStorage();
            addLog('💾 تم حفظ المشروعات في النظام', 'success');
            updateStatus('تم إنشاء المشروعات التجريبية بنجاح', 'success');
        }

        function setupAllTestData() {
            addLog('🚀 إعداد جميع البيانات التجريبية...', 'info');
            createTestEmployees();
            setTimeout(() => {
                createTestProjects();
                setTimeout(() => {
                    addLog('✅ تم إعداد جميع البيانات التجريبية بنجاح', 'success');
                    updateStatus('جميع البيانات التجريبية جاهزة للاختبار', 'success');
                }, 500);
            }, 500);
        }

        function testMonthlyEmployee() {
            addLog('🔧 اختبار فتح نافذة الموظف الشهري...', 'info');
            testsRun++;

            try {
                if (!employees.find(emp => emp.id === 1001)) {
                    addLog('❌ الموظف الشهري غير موجود - يرجى إنشاؤه أولاً', 'error');
                    updateStatus('يرجى إنشاء البيانات التجريبية أولاً', 'error');
                    return;
                }

                if (typeof openDaysCalculator === 'function') {
                    openDaysCalculator(1001);
                    testsSuccess++;
                    addLog('✅ تم فتح نافذة الموظف الشهري بنجاح', 'success');
                    updateStatus('تم فتح نافذة الموظف الشهري - فحص الحقول المفعلة', 'success');
                } else {
                    testsFailed++;
                    addLog('❌ دالة openDaysCalculator غير موجودة', 'error');
                    updateStatus('خطأ: دالة فتح النافذة غير موجودة', 'error');
                }

            } catch (error) {
                testsFailed++;
                addLog('❌ خطأ في فتح نافذة الموظف الشهري: ' + error.message, 'error');
                updateStatus('خطأ في فتح النافذة: ' + error.message, 'error');
            }
        }

        function testDailyEmployee() {
            addLog('🔧 اختبار فتح نافذة الموظف اليومي...', 'info');
            testsRun++;

            try {
                if (!employees.find(emp => emp.id === 1002)) {
                    addLog('❌ الموظف اليومي غير موجود - يرجى إنشاؤه أولاً', 'error');
                    updateStatus('يرجى إنشاء البيانات التجريبية أولاً', 'error');
                    return;
                }

                if (typeof openDaysCalculator === 'function') {
                    openDaysCalculator(1002);
                    testsSuccess++;
                    addLog('✅ تم فتح نافذة الموظف اليومي بنجاح', 'success');
                    updateStatus('تم فتح نافذة الموظف اليومي - فحص الحقول المعطلة', 'success');
                } else {
                    testsFailed++;
                    addLog('❌ دالة openDaysCalculator غير موجودة', 'error');
                    updateStatus('خطأ: دالة فتح النافذة غير موجودة', 'error');
                }

            } catch (error) {
                testsFailed++;
                addLog('❌ خطأ في فتح نافذة الموظف اليومي: ' + error.message, 'error');
                updateStatus('خطأ في فتح النافذة: ' + error.message, 'error');
            }
        }

        function checkMonthlyFields() {
            addLog('🔍 فحص حقول الموظف الشهري...', 'info');

            const fieldsToCheck = [
                'annualVacation', 'substituteVacation', 'emergencyVacation',
                'sickLeave', 'workInjury', 'mission',
                'officialHolidays', 'weeklyRest', 'restAllowance'
            ];

            const results = document.getElementById('fieldTestResults');
            results.innerHTML = '<h4>نتائج فحص الحقول للموظف الشهري:</h4>';

            let enabledCount = 0;
            let totalCount = fieldsToCheck.length;

            fieldsToCheck.forEach(fieldId => {
                const field = document.getElementById(fieldId);
                const resultItem = document.createElement('div');
                resultItem.className = 'field-test-item';

                if (field) {
                    if (!field.disabled) {
                        resultItem.className += ' field-enabled';
                        resultItem.innerHTML = `✅ ${fieldId}<br><small>مفعل</small>`;
                        enabledCount++;
                        addLog(`✅ الحقل ${fieldId} مفعل بشكل صحيح`, 'success');
                    } else {
                        resultItem.className += ' field-disabled';
                        resultItem.innerHTML = `❌ ${fieldId}<br><small>معطل</small>`;
                        addLog(`❌ الحقل ${fieldId} معطل (يجب أن يكون مفعل)`, 'error');
                    }
                } else {
                    resultItem.className += ' field-disabled';
                    resultItem.innerHTML = `❓ ${fieldId}<br><small>غير موجود</small>`;
                    addLog(`❓ الحقل ${fieldId} غير موجود`, 'warning');
                }

                results.appendChild(resultItem);
            });

            const successRate = (enabledCount / totalCount) * 100;
            addLog(`📊 نتيجة الفحص: ${enabledCount}/${totalCount} حقل مفعل (${successRate.toFixed(1)}%)`,
                   successRate === 100 ? 'success' : 'warning');

            updateStatus(`فحص الموظف الشهري: ${enabledCount}/${totalCount} حقل مفعل`,
                        successRate === 100 ? 'success' : 'warning');
        }

        function checkDailyFields() {
            addLog('🔍 فحص حقول الموظف اليومي...', 'info');

            const fieldsToCheck = [
                'annualVacation', 'substituteVacation', 'emergencyVacation',
                'sickLeave', 'workInjury', 'mission',
                'officialHolidays', 'weeklyRest', 'restAllowance'
            ];

            const results = document.getElementById('fieldTestResults');
            results.innerHTML = '<h4>نتائج فحص الحقول للموظف اليومي:</h4>';

            let disabledCount = 0;
            let totalCount = fieldsToCheck.length;

            fieldsToCheck.forEach(fieldId => {
                const field = document.getElementById(fieldId);
                const resultItem = document.createElement('div');
                resultItem.className = 'field-test-item';

                if (field) {
                    if (field.disabled) {
                        resultItem.className += ' field-disabled';
                        resultItem.innerHTML = `✅ ${fieldId}<br><small>معطل بشكل صحيح</small>`;
                        disabledCount++;
                        addLog(`✅ الحقل ${fieldId} معطل بشكل صحيح`, 'success');
                    } else {
                        resultItem.className += ' field-enabled';
                        resultItem.innerHTML = `❌ ${fieldId}<br><small>مفعل (يجب أن يكون معطل)</small>`;
                        addLog(`❌ الحقل ${fieldId} مفعل (يجب أن يكون معطل)`, 'error');
                    }
                } else {
                    resultItem.className += ' field-disabled';
                    resultItem.innerHTML = `❓ ${fieldId}<br><small>غير موجود</small>`;
                    addLog(`❓ الحقل ${fieldId} غير موجود`, 'warning');
                }

                results.appendChild(resultItem);
            });

            const successRate = (disabledCount / totalCount) * 100;
            addLog(`📊 نتيجة الفحص: ${disabledCount}/${totalCount} حقل معطل (${successRate.toFixed(1)}%)`,
                   successRate === 100 ? 'success' : 'warning');

            updateStatus(`فحص الموظف اليومي: ${disabledCount}/${totalCount} حقل معطل`,
                        successRate === 100 ? 'success' : 'warning');
        }

        function testFieldToggling() {
            addLog('🔄 اختبار تبديل نوع الموظف...', 'info');

            // اختبار تبديل من شهري إلى يومي
            addLog('1️⃣ فتح نافذة الموظف الشهري...', 'info');
            testMonthlyEmployee();

            setTimeout(() => {
                addLog('2️⃣ فحص الحقول المفعلة...', 'info');
                checkMonthlyFields();

                setTimeout(() => {
                    addLog('3️⃣ فتح نافذة الموظف اليومي...', 'info');
                    testDailyEmployee();

                    setTimeout(() => {
                        addLog('4️⃣ فحص الحقول المعطلة...', 'info');
                        checkDailyFields();

                        addLog('✅ اكتمل اختبار تبديل نوع الموظف', 'success');
                        updateStatus('اكتمل اختبار تبديل نوع الموظف بنجاح', 'success');
                    }, 1000);
                }, 1000);
            }, 1000);
        }

        function testFieldValidation() {
            addLog('🔍 اختبار التحقق من صحة الحقول...', 'info');

            // فتح نافذة الموظف الشهري
            testMonthlyEmployee();

            setTimeout(() => {
                // محاولة ملء الحقول بقيم تجريبية
                const testFields = [
                    { id: 'annualVacation', value: '5' },
                    { id: 'sickLeave', value: '2' },
                    { id: 'officialHolidays', value: '3' }
                ];

                testFields.forEach(test => {
                    const field = document.getElementById(test.id);
                    if (field && !field.disabled) {
                        field.value = test.value;
                        addLog(`✅ تم ملء الحقل ${test.id} بالقيمة ${test.value}`, 'success');
                    } else if (field && field.disabled) {
                        addLog(`❌ الحقل ${test.id} معطل - لا يمكن ملؤه`, 'error');
                    } else {
                        addLog(`❓ الحقل ${test.id} غير موجود`, 'warning');
                    }
                });

                addLog('✅ اكتمل اختبار التحقق من صحة الحقول', 'success');
                updateStatus('اكتمل اختبار التحقق من صحة الحقول', 'success');
            }, 1000);
        }

        function clearTestData() {
            addLog('🗑️ مسح البيانات التجريبية...', 'warning');

            // إزالة الموظفين التجريبيين
            employees = employees.filter(emp => emp.id !== 1001 && emp.id !== 1002);

            // إزالة المشروعات التجريبية
            projects = projects.filter(proj => proj.id !== 101 && proj.id !== 102);

            // حفظ التغييرات
            saveEmployeesToLocalStorage();
            saveProjectsToLocalStorage();
            populateEmployees();

            // مسح نتائج الاختبار
            const results = document.getElementById('fieldTestResults');
            results.innerHTML = '';

            addLog('✅ تم مسح جميع البيانات التجريبية', 'success');
            updateStatus('تم مسح البيانات التجريبية - جاهز لاختبار جديد', 'success');
        }

        // تشغيل اختبار تلقائي عند تحميل الصفحة
        window.addEventListener('load', function() {
            setTimeout(() => {
                addLog('🚀 تم تحميل صفحة اختبار حقول الموظف الشهري', 'info');
                addLog('💡 استخدم الأزرار أعلاه لبدء الاختبارات', 'info');
                updateStatus('جاهز للاختبار - ابدأ بإعداد البيانات التجريبية', 'info');
            }, 1000);
        });
    </script>
</body>
</html>
