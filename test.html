<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار الأزرار</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            direction: rtl;
        }
        button {
            padding: 10px 20px;
            margin: 10px;
            font-size: 16px;
            cursor: pointer;
            border: none;
            border-radius: 5px;
            color: white;
        }
        .add-btn { background-color: #4CAF50; }
        .table-view-btn { background-color: #673AB7; }
        .import-btn { background-color: #2196F3; }
        .remove-duplicates-btn { background-color: #9C27B0; }
        .search-btn { background-color: #f0f0f0; color: black; }
        
        #result {
            margin-top: 20px;
            padding: 10px;
            background-color: #f9f9f9;
            border-radius: 5px;
        }
    </style>
</head>
<body>
    <h1>اختبار الأزرار</h1>
    
    <div>
        <input type="text" id="searchInput" placeholder="بحث..." style="padding: 8px; margin: 10px;">
        <button class="search-btn" id="searchBtn">🔍</button>
        <button class="add-btn" id="addEmployeeBtn">+ موظف جديد</button>
        <button class="table-view-btn" id="tableViewBtn">عرض جدول</button>
        <button class="import-btn" id="importExcelBtn">استيراد من إكسل</button>
        <button class="remove-duplicates-btn" id="removeDuplicatesBtn">إزالة المكررين</button>
    </div>
    
    <div id="result">
        <p>انقر على أي زر لاختباره...</p>
    </div>

    <script>
        // متغير للاختبار
        var employees = [
            {
                id: 1,
                name: "أحمد محمد",
                position: "مهندس",
                employmentType: "monthly",
                employeeCode: "ENG001",
                nationalId: "12345678901234",
                phone: "01234567890"
            }
        ];

        function showResult(message) {
            document.getElementById('result').innerHTML = '<p style="color: green;">' + message + '</p>';
        }

        function showError(message) {
            document.getElementById('result').innerHTML = '<p style="color: red;">' + message + '</p>';
        }

        // اختبار الأزرار
        document.addEventListener('DOMContentLoaded', function() {
            console.log("تم تحميل الصفحة");

            // زر البحث
            const searchBtn = document.getElementById('searchBtn');
            if (searchBtn) {
                searchBtn.onclick = function() {
                    console.log("تم النقر على زر البحث");
                    showResult("زر البحث يعمل بشكل صحيح!");
                };
                console.log("تم إعداد زر البحث");
            } else {
                console.error("لم يتم العثور على زر البحث");
            }

            // زر إضافة موظف
            const addEmployeeBtn = document.getElementById('addEmployeeBtn');
            if (addEmployeeBtn) {
                addEmployeeBtn.onclick = function() {
                    console.log("تم النقر على زر إضافة موظف");
                    showResult("زر إضافة موظف يعمل بشكل صحيح!");
                };
                console.log("تم إعداد زر إضافة موظف");
            } else {
                console.error("لم يتم العثور على زر إضافة موظف");
            }

            // زر عرض الجدول
            const tableViewBtn = document.getElementById('tableViewBtn');
            if (tableViewBtn) {
                tableViewBtn.onclick = function() {
                    console.log("تم النقر على زر عرض الجدول");
                    showResult("زر عرض الجدول يعمل بشكل صحيح!");
                };
                console.log("تم إعداد زر عرض الجدول");
            } else {
                console.error("لم يتم العثور على زر عرض الجدول");
            }

            // زر استيراد من إكسل
            const importExcelBtn = document.getElementById('importExcelBtn');
            if (importExcelBtn) {
                importExcelBtn.onclick = function() {
                    console.log("تم النقر على زر استيراد من إكسل");
                    showResult("زر استيراد من إكسل يعمل بشكل صحيح!");
                };
                console.log("تم إعداد زر استيراد من إكسل");
            } else {
                console.error("لم يتم العثور على زر استيراد من إكسل");
            }

            // زر إزالة المكررين
            const removeDuplicatesBtn = document.getElementById('removeDuplicatesBtn');
            if (removeDuplicatesBtn) {
                removeDuplicatesBtn.onclick = function() {
                    console.log("تم النقر على زر إزالة المكررين");
                    showResult("زر إزالة المكررين يعمل بشكل صحيح!");
                };
                console.log("تم إعداد زر إزالة المكررين");
            } else {
                console.error("لم يتم العثور على زر إزالة المكررين");
            }

            showResult("تم تحميل جميع الأزرار بنجاح. جرب النقر عليها!");
        });
    </script>
</body>
</html>
