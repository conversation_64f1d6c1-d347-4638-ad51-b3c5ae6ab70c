<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار الوظائف الأساسية</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f8f9fa;
            direction: rtl;
        }
        
        .container {
            max-width: 900px;
            margin: 0 auto;
            background: white;
            padding: 25px;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }
        
        .header {
            text-align: center;
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 25px;
        }
        
        .test-section {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            border-left: 4px solid #007bff;
        }
        
        .button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
            transition: all 0.3s ease;
        }
        
        .button:hover {
            background: #0056b3;
            transform: translateY(-1px);
        }
        
        .button.success { background: #28a745; }
        .button.success:hover { background: #218838; }
        
        .button.danger { background: #dc3545; }
        .button.danger:hover { background: #c82333; }
        
        .status {
            padding: 12px;
            border-radius: 6px;
            margin: 10px 0;
            border-left: 4px solid;
            font-weight: 500;
        }
        
        .status.success {
            background: #d4edda;
            border-left-color: #28a745;
            color: #155724;
        }
        
        .status.error {
            background: #f8d7da;
            border-left-color: #dc3545;
            color: #721c24;
        }
        
        .status.warning {
            background: #fff3cd;
            border-left-color: #ffc107;
            color: #856404;
        }
        
        .status.info {
            background: #d1ecf1;
            border-left-color: #17a2b8;
            color: #0c5460;
        }
        
        .console {
            background: #212529;
            color: #00ff00;
            padding: 15px;
            border-radius: 6px;
            font-family: 'Courier New', monospace;
            font-size: 13px;
            max-height: 350px;
            overflow-y: auto;
            margin: 15px 0;
            border: 1px solid #495057;
        }
        
        .results-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        
        .result-card {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            border: 1px solid #e9ecef;
            text-align: center;
        }
        
        .result-card.success {
            background: #d4edda;
            border-color: #c3e6cb;
        }
        
        .result-card.error {
            background: #f8d7da;
            border-color: #f5c6cb;
        }
        
        .result-icon {
            font-size: 24px;
            margin-bottom: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🧪 اختبار الوظائف الأساسية</h1>
            <p>فحص سريع للتأكد من عمل البرنامج بشكل صحيح</p>
        </div>

        <div class="test-section">
            <h4>🔧 اختبارات سريعة:</h4>
            <button class="button" onclick="testJavaScriptLoading()">فحص تحميل JavaScript</button>
            <button class="button" onclick="testBasicVariables()">فحص المتغيرات الأساسية</button>
            <button class="button" onclick="testCoreFunctions()">فحص الدوال الأساسية</button>
            <button class="button success" onclick="testFullSystem()">اختبار شامل</button>
        </div>

        <div id="status" class="status info">
            <strong>الحالة:</strong> جاهز لبدء الاختبار...
        </div>

        <div id="results" class="results-grid" style="display: none;">
            <!-- سيتم ملؤها ديناميكياً -->
        </div>

        <div class="console" id="console">
            [BASIC_TEST] نظام اختبار الوظائف الأساسية جاهز...<br>
        </div>
    </div>

    <!-- تحميل ملف JavaScript الأساسي -->
    <script src="app.js"></script>
    
    <script>
        function updateStatus(message, type = 'info') {
            const statusDiv = document.getElementById('status');
            statusDiv.className = `status ${type}`;
            statusDiv.innerHTML = `<strong>الحالة:</strong> ${message}`;
        }

        function addLog(message) {
            const console = document.getElementById('console');
            const time = new Date().toLocaleTimeString();
            console.innerHTML += `[${time}] ${message}<br>`;
            console.scrollTop = console.scrollHeight;
        }

        function addResult(title, description, status) {
            const resultsDiv = document.getElementById('results');
            resultsDiv.style.display = 'grid';
            
            const card = document.createElement('div');
            card.className = `result-card ${status}`;
            card.innerHTML = `
                <div class="result-icon">
                    ${status === 'success' ? '✅' : '❌'}
                </div>
                <div style="font-weight: bold; margin-bottom: 5px;">
                    ${title}
                </div>
                <div style="font-size: 12px; color: #6c757d;">
                    ${description}
                </div>
            `;
            
            resultsDiv.appendChild(card);
        }

        function testJavaScriptLoading() {
            addLog('🔍 فحص تحميل JavaScript...');
            
            try {
                // فحص وجود console.log من app.js
                if (typeof console !== 'undefined') {
                    addLog('✓ وحدة التحكم متاحة');
                }
                
                // فحص تحميل ملف app.js
                const scripts = document.querySelectorAll('script[src]');
                let appJsFound = false;
                
                scripts.forEach(script => {
                    if (script.src.includes('app.js')) {
                        appJsFound = true;
                        addLog('✓ ملف app.js موجود في DOM');
                    }
                });
                
                if (appJsFound) {
                    addResult('تحميل JavaScript', 'ملف app.js محمل بنجاح', 'success');
                    updateStatus('JavaScript محمل بنجاح', 'success');
                } else {
                    addResult('تحميل JavaScript', 'ملف app.js غير موجود', 'error');
                    updateStatus('خطأ: ملف app.js غير محمل', 'error');
                }
                
            } catch (error) {
                addLog('❌ خطأ في فحص JavaScript: ' + error.message);
                addResult('تحميل JavaScript', 'خطأ في التحميل: ' + error.message, 'error');
                updateStatus('خطأ في فحص JavaScript', 'error');
            }
        }

        function testBasicVariables() {
            addLog('🔍 فحص المتغيرات الأساسية...');
            
            const requiredVariables = [
                { name: 'employees', desc: 'قائمة الموظفين' },
                { name: 'activeEmployees', desc: 'الموظفين النشطين' },
                { name: 'employeeDaysData', desc: 'بيانات أيام العمل' },
                { name: 'projects', desc: 'قائمة المشروعات' }
            ];
            
            let variablesFound = 0;
            
            requiredVariables.forEach(variable => {
                if (typeof window[variable.name] !== 'undefined') {
                    addLog(`✓ متغير ${variable.name}: موجود`);
                    variablesFound++;
                } else {
                    addLog(`❌ متغير ${variable.name}: مفقود`);
                }
            });
            
            const percentage = (variablesFound / requiredVariables.length) * 100;
            
            if (percentage === 100) {
                addResult('المتغيرات الأساسية', 'جميع المتغيرات موجودة', 'success');
                updateStatus('جميع المتغيرات الأساسية موجودة', 'success');
            } else {
                addResult('المتغيرات الأساسية', `${variablesFound}/${requiredVariables.length} متغير موجود`, 'error');
                updateStatus(`${variablesFound}/${requiredVariables.length} متغير موجود فقط`, 'warning');
            }
        }

        function testCoreFunctions() {
            addLog('🔍 فحص الدوال الأساسية...');
            
            const requiredFunctions = [
                { name: 'populateEmployees', desc: 'عرض الموظفين' },
                { name: 'openModal', desc: 'فتح نافذة إضافة موظف' },
                { name: 'searchEmployees', desc: 'البحث في الموظفين' },
                { name: 'saveEmployee', desc: 'حفظ بيانات الموظف' },
                { name: 'loadEmployeesFromLocalStorage', desc: 'تحميل البيانات' },
                { name: 'showTemporaryMessage', desc: 'عرض الرسائل' }
            ];
            
            let functionsFound = 0;
            
            requiredFunctions.forEach(func => {
                if (typeof window[func.name] === 'function') {
                    addLog(`✓ دالة ${func.name}: موجودة`);
                    functionsFound++;
                } else {
                    addLog(`❌ دالة ${func.name}: مفقودة`);
                }
            });
            
            const percentage = (functionsFound / requiredFunctions.length) * 100;
            
            if (percentage === 100) {
                addResult('الدوال الأساسية', 'جميع الدوال موجودة', 'success');
                updateStatus('جميع الدوال الأساسية موجودة', 'success');
            } else if (percentage >= 75) {
                addResult('الدوال الأساسية', `${functionsFound}/${requiredFunctions.length} دالة موجودة`, 'success');
                updateStatus(`معظم الدوال موجودة (${percentage.toFixed(1)}%)`, 'warning');
            } else {
                addResult('الدوال الأساسية', `${functionsFound}/${requiredFunctions.length} دالة موجودة`, 'error');
                updateStatus(`دوال مفقودة (${percentage.toFixed(1)}%)`, 'error');
            }
        }

        function testFullSystem() {
            updateStatus('🚀 بدء الاختبار الشامل...', 'info');
            addLog('=== بدء الاختبار الشامل للنظام ===');
            
            // مسح النتائج السابقة
            document.getElementById('results').innerHTML = '';
            
            // تشغيل جميع الاختبارات
            setTimeout(() => {
                testJavaScriptLoading();
            }, 500);
            
            setTimeout(() => {
                testBasicVariables();
            }, 1500);
            
            setTimeout(() => {
                testCoreFunctions();
            }, 2500);
            
            // النتيجة النهائية
            setTimeout(() => {
                addLog('=== انتهاء الاختبار الشامل ===');
                
                const successCards = document.querySelectorAll('.result-card.success').length;
                const errorCards = document.querySelectorAll('.result-card.error').length;
                const totalCards = successCards + errorCards;
                
                if (errorCards === 0) {
                    updateStatus('🎉 ممتاز! البرنامج يعمل بشكل مثالي', 'success');
                    addLog('🎉 النتيجة: البرنامج يعمل بشكل صحيح!');
                    
                    // اختبار فتح البرنامج الأساسي
                    setTimeout(() => {
                        addLog('🔗 يمكنك الآن فتح index.html للاستخدام العادي');
                        if (confirm('هل تريد فتح البرنامج الأساسي الآن؟')) {
                            window.open('index.html', '_blank');
                        }
                    }, 1000);
                    
                } else {
                    const percentage = (successCards / totalCards) * 100;
                    updateStatus(`⚠️ ${successCards}/${totalCards} اختبار نجح (${percentage.toFixed(1)}%)`, 'warning');
                    addLog(`⚠️ النتيجة: ${errorCards} مشكلة تحتاج مراجعة`);
                }
            }, 4000);
        }

        // فحص فوري عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            addLog('تم تحميل صفحة الاختبار');
            
            // فحص سريع
            setTimeout(() => {
                if (typeof window.employees !== 'undefined') {
                    addLog('✓ البرنامج الأساسي محمل');
                    updateStatus('البرنامج الأساسي محمل - جاهز للاختبار', 'success');
                } else {
                    addLog('⚠️ البرنامج الأساسي غير محمل');
                    updateStatus('تحذير: البرنامج الأساسي غير محمل', 'warning');
                }
            }, 1000);
        });
    </script>
</body>
</html>
